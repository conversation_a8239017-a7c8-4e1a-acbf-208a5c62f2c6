-- ################################################################################################
-- ## !!! Attention, ne pas enlever ces lignes !!!                                               ##
-- ## é è é è = Pour éviter le problème d'encodage au niveau de l'ouverture avec notepad++.      ##
-- ## !!! Attention, ne pas enlever ces lignes !!!                                               ##
-- ################################################################################################

CREATE OR REPLACE PROCEDURE DropFK (tableName IN VARCHAR2, columnName IN VARCHAR2) IS
    key_table_name VARCHAR2(100);
    key_name VARCHAR2(100);
    CURSOR curs IS
        SELECT ucc.TABLE_NAME, ucc.CONSTRAINT_NAME
        FROM USER_CONS_COLUMNS ucc
        JOIN USER_CONSTRAINTS uc ON uc.CONSTRAINT_NAME = ucc.CONSTRAINT_NAME
        WHERE ucc.TABLE_NAME = UPPER(tableName)
          AND ucc.COLUMN_NAME = UPPER(columnName)
          AND uc.CONSTRAINT_TYPE = 'R';
BEGIN
    OPEN curs;
    LOOP
        FETCH curs INTO key_table_name, key_name;
        EXIT WHEN curs%notfound;
        EXECUTE IMMEDIATE ('ALTER TABLE ' || key_table_name || ' DROP CONSTRAINT ' || key_name);
    END LOOP;
    CLOSE curs;
END;
/

CREATE OR REPLACE PROCEDURE AddFK (tableName IN VARCHAR2, columnName IN VARCHAR2, alt IN VARCHAR2) IS
    is_exists INT := 0;
BEGIN
    SELECT CASE
        WHEN EXISTS(
                SELECT 1
                FROM USER_CONS_COLUMNS ucc
                JOIN USER_CONSTRAINTS uc ON uc.CONSTRAINT_NAME = ucc.CONSTRAINT_NAME
                WHERE ucc.TABLE_NAME = UPPER(tableName)
                  AND ucc.COLUMN_NAME = UPPER(columnName)
                  AND uc.CONSTRAINT_TYPE = 'R'
        ) THEN 1 ELSE 0
    END INTO is_exists
    FROM DUAL;
    IF is_exists = 0 THEN
        EXECUTE IMMEDIATE alt;
    END IF;
    EXCEPTION
        WHEN NO_DATA_FOUND THEN is_exists := 1;
END;
/

CREATE OR REPLACE PROCEDURE AddUnique (tableName IN VARCHAR2, constraintName IN VARCHAR2, alt IN VARCHAR2) IS
    is_exists INT := 0;
BEGIN
    SELECT CASE
        WHEN EXISTS(
            SELECT 1
            FROM USER_CONS_COLUMNS ucc
            JOIN USER_CONSTRAINTS uc ON uc.CONSTRAINT_NAME = ucc.CONSTRAINT_NAME
            WHERE ucc.TABLE_NAME = UPPER(tableName)
              AND ucc.CONSTRAINT_NAME = UPPER(constraintName)
              AND uc.CONSTRAINT_TYPE = 'U'
        ) THEN 1 ELSE 0
    END INTO is_exists
    FROM DUAL;
    IF is_exists = 0 THEN
        EXECUTE IMMEDIATE alt;
    END IF;
    EXCEPTION
        WHEN NO_DATA_FOUND THEN is_exists := 1;
END;
/

CREATE OR REPLACE PROCEDURE AddColumn (tableName IN VARCHAR2, columnName IN VARCHAR2, alt IN VARCHAR2) IS
    is_exists INT := 0;
BEGIN
    SELECT CASE
        WHEN EXISTS(
            SELECT 1
            FROM USER_TAB_COLS
            WHERE TABLE_NAME = UPPER(tableName)
              AND COLUMN_NAME = UPPER(columnName)
        ) THEN 1 ELSE 0
    END INTO is_exists
    FROM DUAL;
    IF is_exists = 0 THEN
        EXECUTE IMMEDIATE alt;
    END IF;
    EXCEPTION
        WHEN NO_DATA_FOUND THEN is_exists := 1;
END;
/

CREATE OR REPLACE PROCEDURE SetColumnNull (tableName IN VARCHAR2, columnName IN VARCHAR2) IS
    is_null USER_TAB_COLUMNS.nullable%type;
BEGIN
    SELECT nullable INTO is_null
    FROM user_tab_columns
    WHERE table_name = UPPER(tableName) AND column_name = UPPER(columnName);
    IF is_null = 'N' THEN
        EXECUTE IMMEDIATE ('ALTER TABLE ' || tableName || ' MODIFY ' || columnName || ' NULL');
    END IF;
END;
/

-- !!!!! NE RIEN AJOUTER AVANT CETTE LIGNE
-- !!!!! Mettre toutes les requêtes pour la prochaine mise à jour entre les balises [BEGIN MAJ] et [END MAJ]

-- [BEGIN MAJ]











































































































































































































































































































































































































-- [END MAJ]
