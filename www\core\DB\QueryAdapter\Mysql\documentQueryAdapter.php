<?php

namespace LMBCore\DB\QueryAdapter\Mysql;

use LMBCore\DB\DB;
use LMBCore\DB\QueryAdapter\Interfaces\InterfacedocumentQueryAdapter;
use LMBCore\PDF\PdfTemplate;
use lmbDateInter;
use PDO;

class documentQueryAdapter implements InterfacedocumentQueryAdapter
{
    protected $db;

    public function __construct(\LMBCore\DB\DB $db) {
        $this->db = $db;
    }

    public function getFieldByRef($field, $ref_doc) {
        $query = "SELECT {$field} 
                FROM documents 
                WHERE ref_doc = ?";
        return $this->db->run($query, [$ref_doc]);
    }

    public function getRefFromId($id) {
        $query = "SELECT ref_doc 
                FROM documents 
                WHERE id_doc = ?";
        return $this->db->run($query, [$id]);
    }

    public function getPelConcat(){
        return "concat(pel.nom,' (',pel.code_iso_5c,')') etat_livraison ";
    }
    
    public function getRefFromRefDocInterne($ref_doc_interne) {
        $query = "SELECT ref_doc 
                FROM documents 
                WHERE ref_doc_interne = ?";
        return $this->db->run($query, [$ref_doc_interne]);
    }

    public function getDocumentsTypes($id_type_groupe) {
        $params = [];
        $where = "";
        if ($id_type_groupe) {
            $params[] = $id_type_groupe;
            $where = " AND id_type_groupe = ?";
        }
        $query = "SELECT * 
                FROM documents_types 
                WHERE actif = 1 {$where}
                ORDER BY lib_type_printed";
        return $this->db->run($query, $params);
    }

    public function getInfos_doc($ref_doc) {
        $query = "SELECT ref_doc, id_type_doc, id_etat_doc
                FROM documents
                WHERE ref_doc = ? ";
        return $this->db->run($query, [$ref_doc]);
    }

    public function getId_contactFromRef_doc($ref_doc) {
        $query = "SELECT id_contact 
                FROM documents 
                WHERE ref_doc = ?";
        return $this->db->run($query, [$ref_doc]);

    }

    public function getDispo($ref_doc) {
        $query = "SELECT dl.id_doc_line,dl.ref_doc_line,dl.id_doc_line_parent,dl.ref_doc_line_parent,visible,a.lot, dl.ref_doc, dl.ref_article, dl.lib_article, dl.desc_article, dl.qte, dl.pu_ht, dl.remise, dlc.qte_livree, dlc.qte_preparation, a.modele, a.ref_interne, dl.pu_ttc
                FROM docs_lines_etendues_full dl
                LEFT JOIN articles a ON dl.ref_article = a.ref_article
                LEFT JOIN doc_lines_cdc dlc ON dl.id_doc_line = dlc.id_doc_line
                WHERE ref_doc = ? AND dl.ref_article NOT LIKE 'TAXE%'
                GROUP BY id_doc_line
                ORDER BY a.lot DESC,dl.ordre ASC";
        return $this->db->run($query, [$ref_doc]);
    }

    public function open_doc($ref_doc, $select = "", $left_join = "") {
        $query = "SELECT d.id_doc, d.uuid_lm, d.code_barre, d.id_contact, d.contact_ref_interne, d.nom_contact, d.id_adr_contact, 
            d.adresse_contact, d.code_postal_contact, d.ville_contact, d.tel_contact, d.email_contact,
            d.id_pays_contact, d.id_etat_contact, d.id_zone_vente, d.source_logiciel, d.app_tarifs, 
            d.details_remise_globale, d.description,
            d.societe_adresse_contact, d.nom_adresse_contact, d.prenom_adresse_contact, d.id_coord_contact,
            d.id_etat_doc, d.id_sous_etat_doc, d.verrouille, d.code_affaire, de.lib_etat_doc, de.is_open,
            dt.lib_type_doc, dt.lib_type_printed, dt.code_doc, dt.use_zone_geo, dt.insert_description,
            d.date_creation_doc date_creation, d.date_modif,
            p.pays pays_contact, concat(pe.nom,' (',pe.code_iso_5c,')') etat_contact, d.id_devise, d.taux_conversion, escompte
            " . $select . ", d.cab, d.ref_doc_externe, d.ref_doc_interne, d.id_pdf_template, d.id_email_template, dt.id_type_doc as idtypedocc, d.id_caisse, d.ca_ht, d.ca_ttc, d.montant_ht, d.montant_ttc, d.montant_ht_devise, d.montant_ttc_devise,
            d.am_codes, d.details_interventions_superviseur
            FROM documents d
            JOIN documents_types dt ON d.id_type_doc = dt.id_type_doc
            JOIN documents_etats de ON d.id_etat_doc = de.id_etat_doc
            LEFT JOIN pays p ON p.id_pays = d.id_pays_contact
            LEFT JOIN pays_etats pe ON pe.id_pays_etat = d.id_etat_contact
            " . $left_join . "
            WHERE d.ref_doc = ? ";
        return $this->db->run($query, [$ref_doc]);
    }

    public function insert_document($datas) {
        return $this->db->insert("documents", $datas);
    }

    public function getDocument_type_by_id_type_doc($id_type_doc) {
        $query = 'SELECT dt.lib_type_doc, dt.lib_type_printed, dt.use_zone_geo, dt.insert_description
            FROM documents_types dt
            WHERE dt.id_type_doc = ? ';
        return $this->db->run($query, [$id_type_doc]);
    }

    public function getId_client_categ($ref_doc) {
        $query = "
            SELECT a.id_client_categ FROM annu_client a
            JOIN documents d ON d.id_contact = a.id_contact
            WHERE d.ref_doc = ? AND a.id_client_categ IS NOT NULL
        ";
        return $this->db->run($query, [$ref_doc]);
    }

    public function getId_tarifFromClient($ref_doc) {
        $query = "
            SELECT a.id_tarif FROM annu_client a
            JOIN documents d ON d.id_contact = a.id_contact
            WHERE d.ref_doc = ? AND a.id_tarif IS NOT NULL
        ";
        return $this->db->run($query, [$ref_doc]);
    }

    public function getId_fidelite_programmeFromClient($ref_doc) {
        $query = "
            SELECT DISTINCT a.id_fidelite_programme FROM annu_client a
            JOIN documents d ON d.id_contact = a.id_contact
            WHERE d.ref_doc = ? AND a.id_fidelite_programme IS NOT NULL
        ";
        return $this->db->run($query, [$ref_doc]);
    }

    public function maj_id_caisse($id_terminal, $ref_doc) {
        $query = "UPDATE documents SET id_caisse = ? 
                  WHERE ref_doc = ?";
        return $this->db->run($query, [$id_terminal, $ref_doc]);
    }

    public function getPdf_modele($id_type_doc) {
        $query = "SELECT dt.id_pdf_modele, pt.classe as code_pdf_modele, dt.usage
                FROM doc_modeles_pdf dt
                JOIN pdf_template pt ON pt.id_pdf_template = dt.id_pdf_modele
                WHERE dt.id_type_doc = ?
                ORDER by dt.usage LIMIT 1";
        return $this->db->run($query, [$id_type_doc]);
    }

    public function setModele_pdf_defaut($id_pdf_modele) {
        $query = "UPDATE doc_modeles_pdf 
                 SET doc_modeles_pdf.usage = 'defaut' 
                 WHERE id_pdf_modele = ?";
        return $this->db->run($query, [$id_pdf_modele]);
    }

    public function update_document($datas, $where) {
        return $this->db->update("documents", $datas, $where);
    }

    public function update_document_table_lie($table, $datas, $where) {
        return $this->db->update($table, $datas, $where);
    }

    public function getAdresse_facturation_contact($id_contact) {
        $query = "SELECT id_adr_facturation id_adresse, societe_adresse , nom_adresse , prenom_adresse , text_adresse , code_postal , ville , a2.id_pays , pays, a2.id_etat, pe2.nom etat
                FROM annu_client ac
                JOIN contacts_adresses a2 ON ac.id_adr_facturation = a2.id_adresse AND ac.id_contact = ?
                LEFT JOIN pays p2 ON a2.id_pays = p2.id_pays
                LEFT JOIN pays_etats pe2 ON a2.id_etat = pe2.id_pays_etat";
        return $this->db->run($query, [$id_contact]);
    }

    public function getFirst_adresse_contact($id_contact, $type = "facturation") {
        $query = "SELECT a.id_adresse, text_adresse, code_postal, ville, a.id_pays, p.pays, societe_adresse, nom_adresse, prenom_adresse, a.id_etat, pe.nom etat
                FROM contacts_adresses a
                LEFT JOIN adresses_usage au ON a.id_adresse=au.id_adresse
                LEFT JOIN adresses_types at ON au.id_adresse_type=at.id_adresse_type AND at.actif = 1
                LEFT JOIN pays p ON a.id_pays = p.id_pays
                LEFT JOIN pays_etats pe ON a.id_etat = pe.id_pays_etat
                WHERE id_contact = ?
                ORDER BY " . DB::if("at.ref_adresse_type='{$type}'", "1", "0") . " DESC,ordre ASC LIMIT 1";
        return $this->db->run($query, [$id_contact]);
    }

    public function getFirstAdresseByContactId($id_contact) {
        $query = "SELECT text_adresse, code_postal, ville
                FROM contacts_adresses
                WHERE id_contact = ?
                LIMIT 1";
        return $this->db->run($query, [$id_contact]);
    }

    public function getAdresse_contact($id_adresse) {
        $query = "SELECT id_adresse, societe_adresse , nom_adresse , prenom_adresse , text_adresse , code_postal , ville , a2.id_pays , pays, a2.id_etat, pe2.nom etat
                FROM contacts_adresses a2
                LEFT JOIN pays p2 ON a2.id_pays = p2.id_pays
                LEFT JOIN pays_etats pe2 ON a2.id_etat = pe2.id_pays_etat
                WHERE a2.id_adresse = ?";
        return $this->db->run($query, [$id_adresse]);
    }

    public function getAdresse_livraison_by_id_contact($id_contact) {
        $query = "SELECT id_adr_livraison id_adresse, societe_adresse , nom_adresse , prenom_adresse , text_adresse , code_postal , ville , a2.id_pays , pays, a2.id_etat, pe2.nom etat
                FROM annu_client ac
                JOIN contacts_adresses a2 ON ac.id_adr_livraison = a2.id_adresse AND ac.id_contact = ?
                LEFT JOIN pays p2 ON a2.id_pays = p2.id_pays
                LEFT JOIN pays_etats pe2 ON a2.id_etat = pe2.id_pays_etat";
        return $this->db->run($query, [$id_contact]);
    }

    public function maj_contact($id_contact, $nom_contact, $id_coord_contact, $id_adr_contact, $societe_adresse_contact,
                                $nom_adresse_contact, $prenom_adresse_contact, $adresse_contact, $code_postal_contact,
                                $ville_contact, $id_pays_contact, $id_etat_contact, $app_tarifs, $id_devise,
                                $id_pdf_template, $id_email_template, $ref_doc) {
        $query = "UPDATE documents
            SET id_contact = ?, nom_contact = ?,
            id_coord_contact = ?,
            id_adr_contact = ?,
            societe_adresse_contact = ?,
            nom_adresse_contact = ?,
            prenom_adresse_contact = ?,
            adresse_contact = ?,
            code_postal_contact = ?,
            ville_contact = ?,
            id_pays_contact = ?,
            id_etat_contact = ?,
            app_tarifs = ?,
            id_devise = ?,
            id_pdf_template = ?,
            id_email_template = ?
            WHERE ref_doc = ?";

        return $this->db->run($query, [$id_contact, $nom_contact, $id_coord_contact, $id_adr_contact, $societe_adresse_contact, $nom_adresse_contact,
            $prenom_adresse_contact, $adresse_contact, $code_postal_contact, $ville_contact, $id_pays_contact, $id_etat_contact, $app_tarifs,
            $id_devise, $id_pdf_template, $id_email_template, $ref_doc]);
    }

    public function getReglements_doc($ref_doc) {
        $query = "SELECT rd.id_reglement, COUNT(rd.ref_doc) as nb_docs
            FROM reglements_docs rd
            LEFT JOIN documents d ON d.ref_doc = rd.ref_doc
            WHERE rd.id_reglement IN (
                SELECT id_reglement
                FROM reglements_docs rd
                WHERE rd.ref_doc = ?
            ) AND rd.liaison_valide = '1'
            GROUP BY rd.id_reglement";

        return $this->db->run($query, [$ref_doc]);
    }

    public function maj_contact_reglement($id_contact, $ids_reglement_update) {
        $q_in = implode(',', array_fill(0, count($ids_reglement_update), '?'));
        $query = "UPDATE reglements SET id_contact = ?
                WHERE id_reglement IN ({$q_in})";
        array_unshift($ids_reglement_update, $id_contact);

        return $this->db->run($query, $ids_reglement_update);
    }
    
    public function get_ordre_virement_lie($ref_reglement) {
        $query = "SELECT ov.id_ordre, ov.ref_ordre FROM ordres_virement ov
                LEFT JOIN ordres_virement_contenu ovc ON ovc.id_ordre = ov.id_ordre
                LEFT JOIN echeances e ON e.id_echeance = ovc.id_echeance
                LEFT JOIN reglements_echeances re ON re.id_echeance = e.id_echeance
                LEFT JOIN reglements r ON r.ref_reglement = re.ref_reglement
                WHERE r.ref_reglement = ?
                    LIMIT 1";
        return $this->db->run($query, [$ref_reglement]);
    }

    public function delete_reglements_doc($ref_doc, $ids_reglement = []) {
        $q_in = implode(',', array_fill(0, count($ids_reglement), '?'));
        $query = "DELETE FROM reglements_docs
                WHERE id_reglement IN ({$q_in}) AND ref_doc = ?";
        $ids_reglement[] = $ref_doc;

        return $this->db->run($query, $ids_reglement);
    }

    public function maj_contact_ref_interne($ref_contact, $ref_doc) {
        $query = "UPDATE documents 
                SET contact_ref_interne = ?
                WHERE ref_doc = ?";
        return $this->db->run($query, [$ref_contact, $ref_doc]);

    }

    public function maj_nom_contact($nom_contact, $ref_doc) {
        $query = "UPDATE documents
            SET nom_contact = ?
            WHERE ref_doc = ?";
        return $this->db->run($query, [$nom_contact, $ref_doc]);
    }

    public function getLib_stock($id_stock) {
        $query = "SELECT lib_stock FROM stocks
            WHERE id_stock = ?";
        return $this->db->run($query, [$id_stock]);
    }

    public function maj_adresse_contact($id_coord_contact, $id_adr_contact, $societe_adresse_contact, $nom_adresse_contact,
                                        $prenom_adresse_contact, $adresse_contact, $code_postal_contact, $ville_contact,
                                        $id_pays_contact, $id_etat_contact, $ref_doc) {
        $query = "UPDATE documents
            SET id_coord_contact = ?,
            id_adr_contact = ?,
            societe_adresse_contact = ?,
            nom_adresse_contact = ?,
            prenom_adresse_contact = ?,
            adresse_contact = ?,
            code_postal_contact = ?,
            ville_contact = ?,
            id_pays_contact = ?,
            id_etat_contact = ?
            WHERE ref_doc = ?";
        return $this->db->run($query, [$id_coord_contact, $id_adr_contact, $societe_adresse_contact, $nom_adresse_contact,
            $prenom_adresse_contact, $adresse_contact, $code_postal_contact, $ville_contact,
            $id_pays_contact, $id_etat_contact, $ref_doc]);
    }

    public function delete_doc_zone_geo($ref_doc) {
        $query = "DELETE FROM docs_zones_geo WHERE ref_doc = ?";
        return $this->db->run($query, [$ref_doc]);
    }

    public function insert_doc_zone_geo($datas) {
        return $this->db->insert("docs_zones_geo", $datas);
    }

    public function maj_societe_adresse_contact($societe_adresse_contact, $ref_doc) {
        $query = "UPDATE documents
            SET societe_adresse_contact = ?
            WHERE ref_doc = ?";
        return $this->db->run($query, [$societe_adresse_contact, $ref_doc]);
    }

    public function maj_nom_adresse_contact($nom_adresse_contact, $ref_doc) {
        $query = "UPDATE documents
            SET nom_adresse_contact = ?
            WHERE ref_doc = ?";
        return $this->db->run($query, [$nom_adresse_contact, $ref_doc]);
    }

    public function maj_prenom_adresse_contact($prenom_adresse_contact, $ref_doc) {
        $query = "UPDATE documents
            SET prenom_adresse_contact = ?
            WHERE ref_doc = ?";
        return $this->db->run($query, [$prenom_adresse_contact, $ref_doc]);
    }

    public function maj_text_adresse_contact($adresse_contact, $ref_doc) {
        $query = "UPDATE documents
            SET adresse_contact = ?
            WHERE ref_doc = ?";
        return $this->db->run($query, [$adresse_contact, $ref_doc]);
    }

    public function maj_text_code_postal_contact($code_postal_contact, $ref_doc) {
        $query = "UPDATE documents
            SET code_postal_contact = ?
            WHERE ref_doc = ?";
        return $this->db->run($query, [$code_postal_contact, $ref_doc]);
    }

    public function maj_text_ville_contact($ville_contact, $ref_doc) {
        $query = "UPDATE documents
            SET ville_contact = ?
            WHERE ref_doc = ?";
        return $this->db->run($query, [$ville_contact, $ref_doc]);
    }

    public function maj_text_id_pays_contact($id_pays_contact, $ref_doc) {
        $query = "UPDATE documents
            SET id_pays_contact = ?
            WHERE ref_doc = ?";
        return $this->db->run($query, [$id_pays_contact, $ref_doc]);
    }

    public function getPaysEtat($id_pays) {
        $query = "SELECT id_pays_etat, nom, code_iso_5c
                FROM pays_etats pe
                JOIN pays p ON pe.id_pays = p.id_pays
                WHERE p.id_pays = ? AND use_etat = 1 
                ORDER BY nom";
        return $this->db->run($query, [$id_pays]);

    }

    public function maj_text_id_etat_contact($id_etat_contact, $ref_doc) {
        $query = "UPDATE documents
            SET id_etat_contact = ?
            WHERE ref_doc = ?";
        return $this->db->run($query, [$id_etat_contact, $ref_doc]);
    }

    public function maj_description($ref_doc, $new_description) {
        $query = "UPDATE documents
                SET description = ?
                 WHERE ref_doc = ?";
        return $this->db->run($query, [$new_description, $ref_doc]);
    }

    public function maj_app_tarifs($ref_doc, $new_app_tarifs) {
        $query = "UPDATE documents
                SET app_tarifs = ?
                 WHERE ref_doc = ?";
        return $this->db->run($query, [$new_app_tarifs, $ref_doc]);
    }

    public function maj_ref_doc_externe($ref_doc_externe, $ref_doc) {
        $query = "UPDATE documents
            SET ref_doc_externe = ?
            WHERE ref_doc = ?";
        return $this->db->run($query, [$ref_doc_externe, $ref_doc]);
    }

    public function getReglements_queamoi($ref_doc) {
        $query = "SELECT DISTINCT ref_reglement,(GROUP_CONCAT( DISTINCT ref_doc ORDER BY ref_doc ASC)) on_docs
            FROM reglements r
            LEFT JOIN reglements_docs rd ON rd.id_reglement = r.id_reglement
            GROUP BY r.id_reglement
            HAVING on_docs= ?";
        return $this->db->run($query, [$ref_doc]);
    }

    public function maj_taux_conversion($taux_conversion, $ref_doc) {
        $query = "UPDATE documents
            SET taux_conversion = ?
            WHERE ref_doc = ?";
        $this->db->run($query, [$taux_conversion, $ref_doc]);

        $query = "UPDATE docs_lines
            SET taux_change = ?
            WHERE ref_doc = ?";
        return $this->db->run($query, [$taux_conversion, $ref_doc]);
    }

    public function maj_id_devise($id_devise, $ref_doc) {
        $query = "UPDATE documents
            SET id_devise = ?
            WHERE ref_doc = ?";
        $this->db->run($query, [$id_devise, $ref_doc]);

        $query = "UPDATE docs_lines
            SET id_devise = ?
            WHERE ref_doc = ?";
        return $this->db->run($query, [$id_devise, $ref_doc]);
    }

    public function getId_devise_by_abrev($abrev_devise) {
        $query = "SELECT id_devise 
                FROM devises 
                WHERE abrev_devise = ?";
        return $this->db->run($query, [$abrev_devise]);
    }

    public function update_liaison_doc_destination($ref_doc_to, $ref_doc_from) {
        $query = "UPDATE IGNORE documents_liaisons 
                SET ref_doc_destination = ?
                WHERE ref_doc_destination = ?";
        return $this->db->run($query, [$ref_doc_to, $ref_doc_from]);
    }

    public function update_liaison_doc_source($ref_doc_to, $ref_doc_from) {
        $query = "UPDATE IGNORE documents_liaisons 
                SET ref_doc_source = ?
                WHERE ref_doc_source = ?";
        return $this->db->run($query, [$ref_doc_to, $ref_doc_from]);
    }

    public function update_reglement_doc($ref_doc_to, $ref_doc_from) {
        $query = "UPDATE reglements_docs SET ref_doc = ?
            WHERE ref_doc = ?";
        return $this->db->run($query, [$ref_doc_to, $ref_doc_from]);
    }

    public function getDocument_etats($id_etat_doc) {
        $query = "SELECT lib_etat_doc, is_open 
                FROM documents_etats
                WHERE id_etat_doc = ?";
        return $this->db->run($query, [$id_etat_doc]);
    }

    public function maj_etat_doc($new_etat_doc, $ref_doc) {
        $query = "UPDATE documents SET id_etat_doc = ?, id_sous_etat_doc = NULL
            WHERE ref_doc = ?";
        return $this->db->run($query, [$new_etat_doc, $ref_doc]);
    }

    public function getDocument_sous_etats($id_etat_doc, $sous_etat) {
        $filed = "dse.ref_doc_sous_etat";
        if (is_numeric($sous_etat)) {
            $filed = "dse.id_doc_sous_etat";
        }
        $query = "SELECT id_doc_sous_etat, lib 
                FROM documents_sous_etats dse
                JOIN documents_etats de ON dse.id_etat_doc = de.id_etat_doc
                WHERE de.id_etat_doc = ? AND {$filed} = ?";
        return $this->db->run($query, [$id_etat_doc, $sous_etat]);
    }

    public function maj_sous_etat_doc($id_sous_etat_doc, $ref_doc) {
        $query = "UPDATE documents d
                SET id_sous_etat_doc = ?
                WHERE ref_doc = ?";
        return $this->db->run($query, [$id_sous_etat_doc, $ref_doc]);
    }

    public function getListe_sous_etats($id_etat_doc) {
        $query = "SELECT id_doc_sous_etat, lib, ref_doc_sous_etat FROM documents_sous_etats dse
            JOIN documents_etats de ON dse.id_etat_doc = de.id_etat_doc
            WHERE de.id_etat_doc = ?
            ORDER BY dse.ordre";
        return $this->db->run($query, [$id_etat_doc]);
    }

    public function getSous_etat($id_sous_etat) {
        $query = "SELECT id_doc_sous_etat, lib, ref_doc_sous_etat ref, ordre
                FROM documents_sous_etats dse
                WHERE dse.id_doc_sous_etat = ?";
        return $this->db->run($query, [$id_sous_etat]);
    }

    public function maj_etat_liaisons($ref_doc, $active) {
        $query = "UPDATE documents_liaisons dl
            LEFT JOIN documents ds ON ds.ref_doc=dl.ref_doc_source
            LEFT JOIN documents dd ON dd.ref_doc=dl.ref_doc_destination
            SET active = :active
            WHERE (ref_doc_source = :ref_doc AND dd.id_etat_doc NOT IN(2,7,12,17,21,26,30,33,37,43,45,48,53,60,66,69,72,80,84,93))
            OR (ref_doc_destination = :ref_doc AND ds.id_etat_doc NOT IN(2,7,12,17,21,26,30,33,37,43,45,48,53,60,66,69,72,80,84,93))";
        return $this->db->run($query, [':active' => $active, ':ref_doc' => $ref_doc]);
    }

    public function getDoc_ligne_infos($ref_doc) {
        $select[] = "SUM(".DB::if("dl.is_chiffre_affaires = 1", "dl.montant_ht", 0) . ") as ca_ht";
        $select[] .= "SUM(".DB::if("dl.is_chiffre_affaires = 1", "dl.montant_ttc", 0) . ") as ca_ttc";
        $query_select = implode(", ", $select);

        $query = "SELECT 
                        $query_select,
                        SUM(dl.montant_ht) as montant_ht,
                        SUM(dl.montant_ttc) as montant_ttc,
                        SUM(dl.montant_ht_devise) as montant_ht_devise,
                        SUM(dl.montant_ttc_devise) as montant_ttc_devise
                    FROM docs_lines dl
                    WHERE dl.ref_doc = ?
                        AND dl.ref_article NOT IN ('INFO', 'INFORMATION', 'SSTOTAL')
                        AND dl.ref_article NOT LIKE 'TAXE%'
                        AND dl.visible = 1";
					
        return $this->db->run($query, [$ref_doc]);
    }

    public function updateDocumentMontants($ca_ht, $ca_ttc, $montant_ht, $montant_ttc, $montant_ht_devise, $montant_ttc_devise, $ref_doc) {
        $query = "UPDATE documents 
                  SET ca_ht = ?,
                    ca_ttc = ?,
                    montant_ht = ?,
                    montant_ttc = ?,
                    montant_ht_devise = ?,
                    montant_ttc_devise = ?
                  WHERE ref_doc = ? ";
        return $this->db->run($query, [$ca_ht, $ca_ttc, $montant_ht, $montant_ttc, $montant_ht_devise, $montant_ttc_devise, $ref_doc]);
    }

    public function getPiecesJointes($liste_cibles) {
        if (!is_array($liste_cibles)) {
            $liste_cibles = [$liste_cibles];
        }
        $q_cible = implode(',', array_fill(0, count($liste_cibles), '?'));
        $query = "SELECT pa.id_piece, pa.type_objet, pa.ref_objet, ps.lib_piece,ps.id_piece_type,ps.nom, ps.taille_piece, pg.inclusion_fichent as cochee, ps.fichier
            FROM pieces_associations pa
            INNER JOIN pieces ps ON ps.id_piece = pa.id_piece
            INNER JOIN pieces_types_mails_params pg ON ps.id_piece_type= pg.id_piece_type
            WHERE pa.ref_objet IN ({$q_cible}) AND pg.inclusion_fichent > 0 
            ORDER BY pa.type_objet";
        return $this->db->run($query, $liste_cibles);
    }

    public function charger_contenu_old($ref_doc, $query_infos_supp) {
        $query = "SELECT dl.ref_doc_line,dl.id_doc_line, dl.ref_article, dl.lib_article, dl.desc_article, dl.ref_suivi_client,
            dl.qte, dl.pu_ht, dl.remise, dl.details_remise, dl.id_tva, dl.tva,
            dl.ordre, dl.id_doc_line_parent, dl.ref_doc_line_parent, dl.visible, dl.pa_ht, dl.pa_forced, dl.fd_ht, dl.fd_forced, dl.montant_ttc, dl.net_ht, dl.montant_tva, dl.pu_ttc,
            a.ref_oem, a.ref_interne, ac.ref_art_categ,a.id_modele_spe, a.id_unite_gestion, a.valo_indice, a.gestion_sn, a.gestion_sn_auto, a.gestion_sn_cachee, a.gestion_sn_liaison_mode, a.gestion_sn_liaison_mode_type, a.modele, a.lot, av.abrev abrev_valo, a.fifo,
            dl.price_nb_decimales
            " . $query_infos_supp['select'] . "
            FROM docs_lines_etendues_full dl
            LEFT JOIN articles a ON dl.ref_article = a.ref_article
            LEFT JOIN art_categs ac ON a.id_art_categ = ac.id_art_categ
            LEFT JOIN articles_unites_gestion av ON av.id_unite_gestion = a.id_unite_gestion
            " . $query_infos_supp['left_join'] . "
            WHERE ref_doc = ?
            GROUP by dl.ref_doc_line
            ORDER BY dl.ordre ";
        return $this->db->run($query, [$ref_doc]);
    }

    public function getCalcul_doc_line($id_doc_line) {
        $query = "SELECT calcul FROM docs_lines_calculs where id_doc_line = ?";
        return $this->db->run($query, [$id_doc_line]);
    }

    public function getInfos_doc_line_sn($query_sn_infos_supp, $ids_doc_line, $order = true) {
        $q_in = implode(',', array_fill(0, count($ids_doc_line), '?'));
        $query = "SELECT dls.id_doc_line_sn, dls.id_doc_line, dls.type_sn, dls.numero_serie, dls.epc, dls.date, dls.sn_qte, dls.id_objet, dls.type_objet " . $query_sn_infos_supp['select'] . "
            FROM docs_lines_sn dls " . $query_sn_infos_supp['left_join'] . "
            WHERE dls.id_doc_line IN (" . $q_in . ")
            GROUP BY dls.id_doc_line,dls.id_doc_line_sn, dls.numero_serie ";
        if ($order) {
            $query .= " ORDER BY CAST(dls.numero_serie AS UNSIGNED) DESC";
        }
        return $this->db->run($query, $ids_doc_line);
    }

    public function charger_contenu($ref_doc, $query_select, $group, $order = null) {
        $order = !empty($order) ? $order : "id_doc_line_parent, dl.ordre";
        $query = $query_select . "
                WHERE dl.ref_doc = ?
                GROUP by dl.id_doc_line
                ORDER BY " . $order;
        return $this->db->run($query, [$ref_doc]);
    }
    public function getDoc_line_select(){
        $select[] = "dl.id_doc_line, dl.ref_doc_line, dl.ref_doc, dl.id_doc_line_parent, dl.ref_doc_line_parent, dl.ordre, dl.visible, dl.details_pu, dl.details_pu codec_pu_details";
        $select[] = "dl.ref_article, dl.lib_article, dl.desc_article,  dl.ref_suivi_client, dl.id_doc_line_from";
        $select[] = "dl.qte, dl.details_qte, dl.qte_retour, dl.details_qte_retour, dl.details_remise, dl.id_tva, dl.tva";
        $select[] = "dl.pu_ht, dl.pu_ttc, dl.pu_ht_devise, dl.pu_ttc_devise, dl.net_ht, dl.net_ht_devise, dl.montant_tva, dl.price_nb_decimales nb_decimales, dl.qte_nb_decimales, dl.price_nb_decimales";
        $select[] = "dl.montant_ht, dl.montant_ttc, dl.montant_ht_devise, dl.montant_ttc_devise";
        $select[] = "dl.pa_ht, dl.pa_forced, dl.fd_ht, dl.fd_forced";

        $select[] = "ac.ref_art_categ, a.id_modele_spe, a.id_unite_gestion, a.valo_indice";
        $select[] = "a.ref_oem, a.ref_interne, a.modele, a.lot, a.fifo";
        $select[] = "a.gestion_sn, a.gestion_sn_auto, a.gestion_sn_cachee, a.gestion_sn_liaison_mode, a.gestion_sn_liaison_mode_type";

        $select[] = "av.abrev abrev_valo";
        $select[] = "dlcalc.calcul";
        $select[] = "docl.detail_vendeur, docl.codec_personnalisation, docl.uuid_lm, docl.id_devise, docl.taux_change";
        $select[] = "docl.montant_ht_devise_hors_rem_globale, docl.montant_ttc_devise_hors_rem_globale";
        $select[] = "docl.infos_supp, docl.id_zone_vente";
        $select[] = "docl.is_chiffre_affaires";

        return $select;
    }
    public function getDoc_line_group(){
        return [];
    }

    public function charger_line($ref_doc_line, $query_select) {
        $query = $query_select . "
                WHERE dl.ref_doc_line = :ref_doc_line OR dl.id_doc_line = :ref_doc_line";
        return $this->db->run($query, [':ref_doc_line' => $ref_doc_line]);
    }

    public function getIdFromUuid_doc_line($uuid_doc_line) {
        $query = "SELECT id_doc_line FROM docs_lines dl
                WHERE dl.uuid_lm = :uuid_doc_line";
        return $this->db->run($query, [':uuid_doc_line' => $uuid_doc_line]);
    }

    public function getInfos_doc_line_sn_fifo($id_doc_line, $ref_stock_article = false) {
        $select = $join = "";
        $params[':id_doc_line'] = $id_doc_line;
        if (!empty($ref_stock_article)) {
            $select = ", sas.sn_qte stock_sn";
            $join = "LEFT JOIN stocks_articles_sn sas ON sas.ref_stock_article= :ref_stock_article AND dls.numero_serie=sas.numero_serie";
            $params[':ref_stock_article'] = $ref_stock_article;
        }
        $query = "SELECT dls.id_doc_line_sn, dls.id_doc_line, dls.numero_serie, dls.id_objet, dls.type_objet,
                    dls.sn_qte {$select}
                   FROM docs_lines_sn dls {$join}
                   WHERE dls.id_doc_line = :id_doc_line GROUP BY id_doc_line_sn";
        return $this->db->run($query, $params);
    }

    public function getInfos_doc_line_by_parent($id_doc_line_parent) {
        $query = "SELECT id_doc_line, ref_doc_line, qte, lib_article
                FROM docs_lines
                WHERE id_doc_line_parent = ?";
        return $this->db->run($query, [$id_doc_line_parent]);
    }

    public function delete_docs_lines($ref_doc, $ref_article = null) {
        $params = [$ref_doc];
        $query = "DELETE  FROM  docs_lines
                WHERE ref_doc = ?";

        if (!empty($ref_article)) {
            $query .= " AND ref_article = ?";
            $params[] = $ref_article;
        }
        return $this->db->run($query, $params);
    }

    public function maj_escompte($ref_doc, $escompte) {
        $query = "UPDATE documents 
                  SET escompte = ? 
                  WHERE ref_doc = ?";
        return $this->db->run($query, [$escompte, $ref_doc]);
    }

    public function charger_contenu_materiel($select, $ref_doc) {
        $query = $select . "
                WHERE dl.ref_doc = ? AND a.modele = ? AND a.lot != ? ";
        return $this->db->run($query, [$ref_doc, \article::BDD_MODELES_MATERIEL, \article::BDD_LOT_COMPO_INTERNE]);

    }

    public function charger_contenu_service_abo($select, $ref_doc) {
        $query = $select . "
                WHERE dl.ref_doc = ? AND a.modele = ?
                GROUP BY dl.id_doc_line";
        return $this->db->run($query, [$ref_doc, \article::BDD_MODELES_SERVICE_ABO_NEW]);
    }

    public function charger_contenu_service_conso($select, $ref_doc) {
        $query = $select . "
                WHERE dl.ref_doc = ? AND a.modele = ?
                GROUP BY dl.id_doc_line";
        return $this->db->run($query, [$ref_doc, \article::BDD_MODELES_SERVICE_CONSO]);
    }

    public function charger_contenu_service_credit($select, $ref_doc) {
        $query = $select . "
                WHERE dl.ref_doc = ? AND a.modele = ? AND a.lot != ?
                GROUP BY dl.id_doc_line";
        return $this->db->run($query, [$ref_doc, \article::BDD_MODELES_CREDIT, \article::BDD_LOT_COMPO_INTERNE]);
    }

    public function delete_doc_line_by_id($id_doc_line) {
        $query = "DELETE FROM docs_lines WHERE id_doc_line = ?";
        return $this->db->run($query, [$id_doc_line]);
    }

    public function getInfos_doc_line_by_ref_doc($ref_doc) {
        $query = "SELECT id_doc_line, ref_doc_line FROM docs_lines WHERE ref_doc = ?";
        return $this->db->run($query, [$ref_doc]);
    }

    public function insert_doc_line_liaison($id_doc_line, $id_liaison_type, $ref_objet_lie) {
        $query = "INSERT INTO docs_lines_liaisons(id_doc_line,id_liaison_type,ref_objet_lie)
                VALUES(?,?,?)";
        return $this->db->run($query, [$id_doc_line, $id_liaison_type, $ref_objet_lie]);
    }

    public function getLine_liaison($id_doc_line, $type = false) {
        $params = [$id_doc_line];
        $q_in = " ";
        if (!empty($type)) {
            if (!is_array($type)) {
                $type = [$type];
            }
            $in = implode(',', array_fill(0, count($type), '?'));
            $params = array_merge($params, $type);
            $q_in = " AND id_liaison_type IN ({$in})";
        }
        $query = "SELECT id_liaison_type, ref_objet_lie
                    FROM docs_lines_liaisons WHERE id_doc_line = ? {$q_in}";
        return $this->db->run($query, $params);
    }

    public function existsLink($ref_doc, $id_liaison_type, $ref_objet_lie) {
        $query = "SELECT dl.id_doc_line, count(DISTINCT dl.id_doc_line) nb
                    FROM docs_lines_liaisons dll
                    JOIN docs_lines dl ON dl.id_doc_line = dll.id_doc_line
                    WHERE ref_doc = ? AND id_liaison_type = ? AND ref_objet_lie = ?";
        return $this->db->run($query, [$ref_doc, $id_liaison_type, $ref_objet_lie]);
    }

    public function getPu_total_service_regie($id_doc_line_parent) {
        $query = "SELECT IFNULL(sum(pu_ht*qte*(1-remise/100)), 0) pu_total
                    FROM docs_lines_etendues_full dl 
                    WHERE id_doc_line_parent = ?
                    GROUP BY id_doc_line_parent";
        return $this->db->run($query, [$id_doc_line_parent]);
    }

    public function insert_doc_line($datas, $auto_update = []) {
        return $this->db->insert("docs_lines", $datas, $auto_update);
    }

    public function update_doc_line($datas, $where) {
        return $this->db->update("docs_lines", $datas, $where);
    }

    public function getInfos_doc_line_id($id_doc_line) {
        $query = "SELECT ref_doc, id_doc_line_parent, ordre, ref_article
                FROM docs_lines
                WHERE id_doc_line = ?";
        return $this->db->run($query, [$id_doc_line]);
    }

    public function getMax_ordre_doc_line($ref_doc, $id_doc_line_parent = NULL) {
        $query = "SELECT MAX(ordre) ordre 
                  FROM docs_lines
                  WHERE ref_doc = ? AND COALESCE(id_doc_line_parent,0) = ? ";
        return $this->db->run($query, [$ref_doc, $id_doc_line_parent ?? 0]);
    }

    public function maj_line_lib_article($lib_article, $ref_doc_line) {
        $query = "UPDATE docs_lines 
                  SET lib_article = ?
                   WHERE ref_doc_line = ?";
        return $this->db->run($query, [$lib_article, $ref_doc_line]);
    }

    public function maj_line_id_client($new_id_client, $ref_doc_line) {
        $query = "UPDATE docs_lines 
                  SET ref_suivi_client = ?
                   WHERE ref_doc_line = ?";
        return $this->db->run($query, [$new_id_client, $ref_doc_line]);
    }

    public function maj_line_desc_article($desc_article, $ref_doc_line) {
        $query = "UPDATE docs_lines 
                  SET desc_article = ?
                   WHERE ref_doc_line = ?";
        return $this->db->run($query, [$desc_article, $ref_doc_line]);
    }

    public function maj_line_ref_article($new_ref_article, $ref_doc_line) {
        $query = "UPDATE docs_lines 
                  SET ref_article = ?
                   WHERE ref_doc_line = ?";
        return $this->db->run($query, [$new_ref_article, $ref_doc_line]);
    }

    public function maj_line_id_doc_line_parent($new_id_doc_line_parent, $id_doc_line) {
        $query = "UPDATE docs_lines 
                  SET id_doc_line_parent = ?
                   WHERE id_doc_line = ?";
        return $this->db->run($query, [$new_id_doc_line_parent, $id_doc_line]);
    }

    public function replace_line_codec_pu_details($new_codec_pu_article, $ref_doc_line) {
        $query = "UPDATE docs_lines 
                  SET details_pu = ?
                   WHERE ref_doc_line = ?";
        return $this->db->run($query, [$new_codec_pu_article, $ref_doc_line]);
    }

    public function replace_line_details_qte_article($new_codec_qte_article, $ref_doc_line, $use_uuid = false) {
        $params = [$new_codec_qte_article, $ref_doc_line];
        $query = "UPDATE docs_lines 
                  SET details_qte = ?
                   WHERE ref_doc_line = ?";
        if ($use_uuid) {
            $query .= " OR uuid_lm = ?";
            $params[] = $ref_doc_line;
        }
        return $this->db->run($query, $params);
    }

    public function maj_line_codec_personnalisation($new_codec, $ref_doc_line) {
        $query = "UPDATE docs_lines 
                  SET codec_personnalisation = ?
                   WHERE ref_doc_line = ?";
        return $this->db->run($query, [$new_codec, $ref_doc_line]);
    }

    public function maj_line_infos_supp_raw($infos_supp, $ref_doc_line) {
        $query = "UPDATE docs_lines 
                  SET infos_supp = ?
                   WHERE ref_doc_line = ?";
        return $this->db->run($query, [$infos_supp, $ref_doc_line]);
    }

    public function maj_line_id_zone_vente($ref_doc_line, $id_zone_vente) {
        $query = "UPDATE docs_lines 
                SET id_zone_vente = ?
                WHERE ref_doc_line = ? ";
        return $this->db->run($query, [$id_zone_vente ?? "", $ref_doc_line]);
    }

    public function bloque_pa($ref_doc) {
        $query = "UPDATE docs_lines 
                SET pa_forced=1 
                WHERE pa_forced = 0 AND ref_doc = ?";
        return $this->db->run($query, [$ref_doc]);
    }

    public function getInfos_from_id_doc_line($id_doc_line) {
        $query = "SELECT dl.id_doc_line,dl.ref_doc_line,ref_article , qte, GROUP_CONCAT(numero_serie SEPARATOR '||') as sn
            FROM docs_lines dl
            LEFT JOIN docs_lines_sn dls ON dls.id_doc_line = dl.id_doc_line
            WHERE dl.id_doc_line = ?
            GROUP BY dl.id_doc_line";
        return $this->db->run($query, [$id_doc_line]);
    }

    public function existe_numero_serie($numero_serie) {
        $query = "SELECT numero_serie
            FROM stocks_articles_sn sas
            WHERE sas.numero_serie = ?";
        return $this->db->run($query, [$numero_serie]);
    }

    public function add_line_sn($id_doc_line, $numero_serie) {
        $query = "INSERT INTO docs_lines_sn (id_doc_line, numero_serie)
            VALUES (?, ?)";
        return $this->db->run($query, [$id_doc_line, $numero_serie]);
    }

    public function update_reference_tag_sn($last_id) {
        global $ARTICLE_ID_REFERENCE_SN;
        $query = "UPDATE references_tags 
                SET last_id = ?
                WHERE id_reference = ? AND last_id < ?";
        return $this->db->run($query, [$last_id, $ARTICLE_ID_REFERENCE_SN, $last_id]);
    }

    public function insert_doc_line_sn($data, $auto_update = []) {
        return $this->db->insert("docs_lines_sn", $data, $auto_update);
    }

    public function update_doc_line_sn($data, $where) {
        return $this->db->update("docs_lines_sn", $data, $where);
    }

    public function insert_doc_line_calcul($id_doc_line, $calcul) {
        $query = "INSERT INTO docs_lines_calculs (id_doc_line,calcul) 
        VALUES (?, ?)";
        return $this->db->run($query, [$id_doc_line, $calcul]);
    }

    public function del_line_sn($id_doc_line_sn) {
        $query = "DELETE FROM docs_lines_sn
            WHERE id_doc_line_sn = ?";
        return $this->db->run($query, [$id_doc_line_sn]);
    }

    public function del_line_sn_by_id_doc_line($id_doc_line) {
        $query = "DELETE FROM docs_lines_sn
            WHERE id_doc_line = ?";
        return $this->db->run($query, [$id_doc_line]);
    }

    public function getId_doc_line_sn($id_doc_line) {
        $query = "SELECT id_doc_line_sn 
                FROM docs_lines_sn
                WHERE id_doc_line = ?";
        return $this->db->run($query, [$id_doc_line]);
    }

    public function getInfos_doc_line_sn_by_field($field, $value, $where_objet = false) {
        $query = "SELECT  type_objet, id_objet, id_doc_line
                FROM docs_lines_sn
                WHERE {$field} = ?";
        if ($where_objet) {
            $query .= " AND type_objet = 'objets'";
        }
        return $this->db->run($query, [$value]);
    }

    public function delete_objet($id_objet) {
        $query = "DELETE FROM objets WHERE id_objet = ?";
        return $this->db->run($query, [$id_objet]);
    }

    public function getTotal_unused_line_sn($id_doc_line, $nb_decimal) {
        $query = "SELECT count(dls.id_doc_line_sn) nb, dls.id_doc_line, ROUND(SUM(dls.sn_qte), $nb_decimal) total_qte
            FROM docs_lines_sn dls
            WHERE dls.id_doc_line = ?
            GROUP BY dls.id_doc_line";
        return $this->db->run($query, [$id_doc_line]);
    }

    public function getId_doc_line_sn_used($id_doc_line) {
        $query = "SELECT dls.id_doc_line_sn
                FROM docs_lines_sn dls
                WHERE dls.id_doc_line = ?
                ORDER BY id_doc_line_sn ASC";
        return $this->db->run($query, [$id_doc_line]);
    }

    public function update_doc_line_sn_qte($id_doc_line_sn, $qte = false) {
        if ($qte) {
            $query = "UPDATE docs_lines_sn 
                    SET sn_qte = ?
                    WHERE id_doc_line_sn = ?";
            return $this->db->run($query, [$qte, $id_doc_line_sn]);
        }
        $query = "DELETE FROM docs_lines_sn
                  WHERE id_doc_line_sn = ?";
        return $this->db->run($query, [$id_doc_line_sn]);
    }

    public function maj_line_sn($id_doc_line_sn, $new_sn, $type_objet = false) {
        $and = $type_objet ? " , type_objet = '{$type_objet}'" : "";
        $query = "UPDATE docs_lines_sn 
                SET numero_serie = ? {$and}
                WHERE id_doc_line_sn = ?";
        return $this->db->run($query, [$new_sn ?? null, $id_doc_line_sn]);
    }

    public function update_objet_doc_line_sn($type_objet, $id_objet, $id_doc_line_sn) {
        $query = "UPDATE docs_lines_sn 
                SET type_objet = ?, id_objet = ?
            WHERE id_doc_line_sn = ?";
        return $this->db->run($query, [$type_objet, $id_objet, $id_doc_line_sn]);
    }

    public function getRef_article_by_id_doc_line_sn($id_doc_line_sn) {
        $query = "SELECT ref_article 
                FROM docs_lines_sn dls
                JOIN docs_lines USING(id_doc_line)
                WHERE dls.id_doc_line_sn = ?";
        return $this->db->run($query, [$id_doc_line_sn]);
    }

    public function getInfos_stock_article_sn($numero_serie) {
        $query = "SELECT * 
                FROM stocks_articles_sn 
                WHERE numero_serie = ?";
        return $this->db->run($query, [$numero_serie]);
    }

    public function getInfos_doc_line_entendue($ref_doc, $lignes) {
        $params = [$ref_doc];
        $q_in = "";
        if (!empty($lignes)) {
            $in = implode(',', array_fill(0, count($lignes), '?'));
            $q_in = " AND dl.ref_doc_line IN ($in) ";
            $params = array_merge($params, $lignes);
        }
        $query = "SELECT dl.id_doc_line,dl.ref_doc_line, dl.ref_article, dl.lib_article, dl.qte, dl.pu_ht, dl.tva, dl.remise,
                MD5(CONCAT(dl.ref_article,dl.lib_article,dl.pu_ht,dl.tva,dl.remise,dl.visible)) hashline
                FROM docs_lines_etendues_full dl
                LEFT JOIN docs_lines dl_enfant ON dl.id_doc_line = dl_enfant.id_doc_line_parent
                WHERE dl_enfant.ref_doc_line IS NULL AND dl.id_doc_line_parent IS NULL AND dl.ref_doc = ? " . $q_in;
        return $this->db->run($query, $params);
    }

    public function update_id_doc_line($id_doc_line, $old_id_doc_line) {
        $query = "UPDATE docs_lines_sn
                SET id_doc_line = ?
                WHERE id_doc_line = ?";
        return $this->db->run($query, [$id_doc_line, $old_id_doc_line]);
    }

    public function detele_line($ids_delete = [], $ids_not_delete = []) {
        $in = implode(',', array_fill(0, count($ids_delete), '?'));
        $not_in = implode(',', array_fill(0, count($ids_not_delete), '?'));
        $params = array_merge($ids_delete, $ids_not_delete);
        $query = "DELETE FROM docs_lines 
                WHERE id_doc_line IN ({$in}) 
                  AND id_doc_line NOT IN ({$not_in})";
        return $this->db->run($query, $params);
    }

    public function update_qte_doc_line($qte, $id_doc_line) {
        $query = "UPDATE docs_lines 
                SET qte = ? , id_doc_line_from = NULL 
                WHERE id_doc_line = ?";
        return $this->db->run($query, [$qte, $id_doc_line]);
    }

    public function get_line_options($ref_doc_line) {
        $query = "SELECT dl2.*
            FROM docs_lines dl
            JOIN articles a ON a.ref_article = dl.ref_article
            JOIN articles_liaisons al ON a.id_article = al.id_article
            JOIN art_liaisons_types alt ON al.id_article_liaison_type = alt.id_article_liaison_type
            JOIN docs_lines_etendues_full dl2 ON dl2.ref_doc=dl.ref_doc 
            JOIN articles a2 ON a2.ref_article = dl2.ref_article AND al.id_article_lie = a2.id_article
            WHERE alt.ref_article_liaison_type = 'option' AND dl.ref_doc_line = ? ";
        return $this->db->run($query, [$ref_doc_line]);
    }

    public function maj_verrouille($verrouille, $ref_doc) {
        $query = "UPDATE documents
                SET verrouille = ?
                WHERE ref_doc = ?";
        return $this->db->run($query, [$verrouille, $ref_doc]);
    }

    public function maj_code_affaire($code_affaire, $ref_doc) {
        $query = "UPDATE documents
                SET code_affaire = ?
                WHERE ref_doc = ?";
        return $this->db->run($query, [$code_affaire, $ref_doc]);
    }

    public function maj_code_barre($code_barre, $ref_doc) {
        $query = "UPDATE documents
                SET code_barre = ?
                WHERE ref_doc = ?";
        return $this->db->run($query, [$code_barre, $ref_doc]);
    }

    public function insert_document_liaison($datas, $auto_update = []) {
        return $this->db->insert("documents_liaisons", $datas, $auto_update);
    }

	public function insert_sa_liaison_document($datas) {
		$query = "INSERT INTO stock_ajustements_liaisons_docs (id_stock_ajustements_liaisons_docs, ref_doc, id_stock_ajustement)
				VALUES (null, :ref_doc, :id_stock_ajustement)";
		return $this->db->run($query, [":ref_doc" => $datas["ref_doc"], ":id_stock_ajustement" => $datas["id_stock_ajustement"]]);
	}

    public function getInfos_doc_line($ref_doc, $lignes) {
        $params = [];
        $q_where = " ";
        if (!empty($ref_doc)) {
            $q_where .= " AND ref_doc = ?";
            $params[] = $ref_doc;
        }
        if (!empty($lignes)) {
            $in = implode(',', array_fill(0, count($lignes), '?'));
            $q_where .= " AND dl.ref_doc_line IN ($in) ";
            $params = array_merge($params, $lignes);
        }
        $query = "SELECT id_doc_line,ref_doc_line, ref_article, qte
                FROM docs_lines dl
                WHERE 1=1 " . $q_where;
        return $this->db->run($query, $params);
    }

    public function getInfos_doc_and_doc_line($lines) {
        $in = implode(',', array_fill(0, count($lines), '?'));
        $query = "SELECT id_doc_line, ref_doc_line, ref_article, qte, d.id_contact
            FROM docs_lines dl
            LEFT JOIN documents d ON d.ref_doc = dl.ref_doc
            WHERE ref_doc_line IN ( {$in} ) ";
        return $this->db->run($query, $lines);
    }

    public function charger_events($ref_doc) {
        $query = "SELECT de.id_doc_event, date_event, de.id_event_type, det.lib_event_type, de.event, de.ref_user, u.pseudo
            FROM documents_events de
            LEFT JOIN documents_events_types det ON de.id_event_type = det.id_event_type
            LEFT JOIN users u ON de.ref_user = u.ref_user
            WHERE ref_doc = ?
            ORDER BY date_event ASC ";
        return $this->db->run($query, [$ref_doc]);
    }

    public function add_event_doc($ref_doc, $date_event, $id_event_type, $event, $ref_user) {
        $query = "INSERT INTO documents_events (id_doc_event, ref_doc, date_event, id_event_type, event, ref_user)
                VALUES (NULL, ?, ?, ?, ?, ?)";
        return $this->db->run($query, [$ref_doc, $date_event, $id_event_type, $event, $ref_user]);
    }

    public function maj_date_creation($date_creation, $ref_doc) {
        $query = "UPDATE documents
                SET date_creation_doc = ?
                WHERE ref_doc = ?";
        return $this->db->run($query, [$date_creation, $ref_doc]);
    }

    public function insert_doc_edition($ref_doc, $id_edition_mode, $information, $date_edition, $ref_user) {
        $query = "INSERT INTO documents_editions (ref_doc, id_edition_mode, information, date_edition, ref_user)
            VALUES (?, ?, ?, {$date_edition}, ?) ";
        return $this->db->run($query, [$ref_doc, $id_edition_mode, $information, $ref_user]);

    }

    public function getRef_doc_source($ref_doc_destination) {
        $query = "SELECT ref_doc_source 
                FROM documents_liaisons
                WHERE ref_doc_destination = ? LIMIT 1;";
        return $this->db->run($query, [$ref_doc_destination]);
    }

    public function getRef_doc_destination($ref_doc_source) {
        $query = "SELECT ref_doc_destination 
                FROM documents_liaisons
                WHERE ref_doc_source = ? LIMIT 1;";
        return $this->db->run($query, [$ref_doc_source]);
    }

    public function charger_liaisons_doc_source($ref_doc) {
        $query = "SELECT ref_doc_source, dl.active, d.id_type_doc, dt.lib_type_doc, d.id_etat_doc, de.lib_etat_doc, d.id_devise, d.taux_conversion,
                d.date_creation_doc date_creation, d.code_affaire,sum(dle.montant_ht) montant_ht, sum(dle.montant_ttc) montant_ttc,sum(dle.montant_ttc_devise) montant_ttc_devise, d.id_contact
                FROM documents_liaisons dl
                LEFT JOIN documents d ON d.ref_doc = dl.ref_doc_source
                LEFT JOIN docs_lines_etendues dle ON d.ref_doc = dle.ref_doc
                LEFT JOIN documents_types dt ON d.id_type_doc = dt.id_type_doc
                LEFT JOIN documents_etats de ON d.id_etat_doc = de.id_etat_doc
                WHERE ref_doc_destination = ? AND dt.actif = 1
                GROUP BY ref_doc_source, dl.active, d.id_type_doc, dt.lib_type_doc, d.id_etat_doc, de.lib_etat_doc, 
                         d.id_devise, d.taux_conversion, d.date_creation_doc, d.code_affaire, d.id_contact
                ORDER BY date_creation ";
        return $this->db->run($query, [$ref_doc]);
    }

    public function charger_liaisons_doc_destination($ref_doc) {
        $query = "SELECT ref_doc_destination, dl.active, d.id_type_doc, dt.lib_type_doc, d.id_etat_doc, de.lib_etat_doc, d.id_devise, d.taux_conversion,
                d.date_creation_doc date_creation, d.code_affaire,sum(dle.montant_ht) montant_ht, sum(dle.montant_ttc) montant_ttc,sum(dle.montant_ttc_devise) montant_ttc_devise, d.id_contact
                FROM documents_liaisons dl
                LEFT JOIN documents d ON d.ref_doc = dl.ref_doc_destination
                LEFT JOIN docs_lines_etendues dle ON d.ref_doc = dle.ref_doc
                LEFT JOIN documents_types dt ON d.id_type_doc = dt.id_type_doc
                LEFT JOIN documents_etats de ON d.id_etat_doc = de.id_etat_doc
                WHERE ref_doc_source = ? AND dt.actif = 1
                GROUP BY ref_doc_destination,dl.active,  d.id_type_doc, dt.lib_type_doc, d.id_etat_doc, de.lib_etat_doc,
                         d.id_devise, d.taux_conversion, d.date_creation_doc, d.code_affaire, d.id_contact
                ORDER BY date_creation";
        return $this->db->run($query, [$ref_doc]);
    }

    public function desactive_liaison_reglement_doc($ref_doc) {
        $query = "UPDATE reglements_docs SET liaison_valide = 0
                WHERE ref_doc = ? AND liaison_valide=1";
        return $this->db->run($query, [$ref_doc]);
    }

    public function getInfos_reglement_doc($ref_doc) {
        $query = "SELECT DISTINCT ref_reglement, montant, rapprochement_manuel 
                FROM reglements_docs WHERE ref_doc = ?";
        return $this->db->run($query, [$ref_doc]);
    }

    public function maj_liaison_doc($active, $ref_doc_source, $ref_doc_destination) {
        $query = "UPDATE documents_liaisons 
                SET active = ?
                WHERE ref_doc_source = ? AND ref_doc_destination = ?";
        return $this->db->run($query, [$active, $ref_doc_source, $ref_doc_destination]);
    }

    public function delete_liaison_doc($ref_doc_source, $ref_doc_destination) {
        $query = "DELETE FROM documents_liaisons
            WHERE (ref_doc_source = :ref_doc_source AND ref_doc_destination = :ref_doc_destination) OR
            (ref_doc_source = :ref_doc_destination AND ref_doc_destination = :ref_doc_source)  ";
        return $this->db->run($query, [":ref_doc_source" => $ref_doc_source, ":ref_doc_destination" => $ref_doc_destination]);
    }

    public function delete_liaison_stock_ajustement($id_stock_ajustement, $ref_doc) {
        $query = "DELETE FROM stock_ajustements_liaisons_docs
            WHERE id_stock_ajustement = :id_stock_ajustement 
            AND ref_doc = :ref_doc";
        return $this->db->run($query, [
            ":id_stock_ajustement" => $id_stock_ajustement,
            ":ref_doc" => $ref_doc
        ]);
    }

    public function delete_liaison_stock_ajustement_by_id($id_stock_ajustements_liaisons_docs) {
        $query = "DELETE FROM stock_ajustements_liaisons_docs
            WHERE id_stock_ajustements_liaisons_docs = :id_liaison";
        return $this->db->run($query, [
            ":id_liaison" => $id_stock_ajustements_liaisons_docs
        ]);
    }

    public function liaisonDocExists($ref_doc_source, $ref_doc_destination) {
        $query = "SELECT COUNT(*) as nb 
                FROM documents_liaisons
                WHERE (ref_doc_source = :ref_doc_source AND ref_doc_destination = :ref_doc_destination) OR
            (ref_doc_source = :ref_doc_destination AND ref_doc_destination = :ref_doc_source)  ";
        return $this->db->run($query, [":ref_doc_source" => $ref_doc_source, ":ref_doc_destination" => $ref_doc_destination]);
    }

    public function update_calcul_doc_line($calcul, $id_doc_line) {
        $query = "UPDATE docs_lines_calculs 
                SET calcul = ?
                WHERE id_doc_line = ?";
        return $this->db->run($query, [$calcul, $id_doc_line]);
    }

    public function getInfos_contenue_line($query_infos_supp, $liste_lines) {
        $in = implode(',', array_fill(0, count($liste_lines), '?'));
        $query = "SELECT dl.id_doc_line,dl.ref_doc_line, dl.ref_article, dl.lib_article, dl.desc_article, 
                dl.ref_suivi_client, dl.qte, dl.pu_ht, dl.price_nb_decimales, dl.details_remise, dl.id_tva, dl.ordre, 
                dl.id_doc_line_parent,dl.ref_doc_line_parent, dl.visible, dl.pa_ht,dl.pa_forced,dl.fd_ht,dl.fd_forced,
                dl.id_devise,
                dl.taux_change,
                dl.pu_ttc,
                dl.pu_ht_devise,
                dl.pu_ttc_devise,
                dl.montant_ht,
                dl.montant_ttc,
                dl.montant_ht_devise,
                dl.montant_ttc_devise,
                dl.montant_ht_devise_hors_rem_globale,
                dl.montant_ttc_devise_hors_rem_globale,
                dl.qte_nb_decimales
            " . $query_infos_supp['select'] . "
            FROM docs_lines dl
            " . $query_infos_supp['left_join'] . "
            WHERE dl.ref_doc_line IN ( {$in})
            ORDER BY id_doc_line_parent, ordre";
        return $this->db->run($query, $liste_lines);
    }

    public function getInfos_doc_line_sn_by_ids($ids_lines) {
        $params = [];
        $q_where = " ";
        if (!empty($ids_lines)) {
            $in = implode(',', array_fill(0, count($ids_lines), '?'));
            $q_where = " AND dls.id_doc_line IN ($in) ";
            $params = array_merge($params, $ids_lines);
        }
        $query = "SELECT id_doc_line, numero_serie,sn_qte, type_objet, id_objet
                FROM docs_lines_sn dls
                WHERE 1=1 " . $q_where;
        return $this->db->run($query, $params);
    }

    public function getInfos_doc_line_calcul($ids_lines) {
        $in = implode(',', array_fill(0, count($ids_lines), '?'));
        $query = "SELECT id_doc_line,calcul 
                FROM docs_lines_calculs 
                WHERE id_doc_line IN (" . $in . ")";
        return $this->db->run($query, $ids_lines);
    }

    public function charger_commerciaux($ref_doc) {
        $query = "SELECT dvc.id_contact, dvc.ref_doc, dvc.detail_part, dvc.part, dvc.mode_attribution,
            a.nom_complet nom, ac.id_commission_regle,  cr.lib_comm, cr.formule_comm
            FROM doc_ventes_commerciaux  dvc
            LEFT JOIN contacts a ON a.id_contact = dvc.id_contact
            LEFT JOIN annu_commercial ac ON ac.id_contact = dvc.id_contact
            LEFT JOIN  commissions_regles  cr ON cr.id_commission_regle = ac.id_commission_regle
            WHERE dvc.ref_doc = ?
            ORDER BY dvc.part DESC ";
        return $this->db->run($query, [$ref_doc]);
    }

    public function getInfos_doc_vente_commercial($ref_doc) {
        $query = "SELECT id_contact, part 
                FROM doc_ventes_commerciaux 
                WHERE ref_doc = ? ";
        return $this->db->run($query, [$ref_doc]);
    }

    public function delete_commercial_doc($ref_doc) {
        $query = "DELETE FROM doc_ventes_commerciaux
                 WHERE ref_doc = ?";
        return $this->db->run($query, [$ref_doc]);
    }

    public function attribution_commercial($id_contact, $ref_doc, $detail_part, $part, $mode_attribution) {
        $query = "INSERT INTO doc_ventes_commerciaux (id_contact, ref_doc, detail_part, part, mode_attribution)
                VALUES (?, ?, ?, ?, ?)";
        return $this->db->run($query, [$id_contact, $ref_doc, $detail_part, $part, $mode_attribution]);
    }

    public function charger_reglements($ref_doc) {
        $query = "SELECT r.id_reglement, r.uuid_lm, r.ref_reglement, r.id_reglement_type, r.id_reglement_type as id_reglement_mode, r.id_reglement_mode_origine, r.id_contact, r.date_reglement, IF(r.montant_reglement-IFNULL(reg_use.montant_rembourse,0)>=0,r.montant_reglement-IFNULL(reg_use.montant_rembourse,0),0) montant_reglement, r.valide, r.taux_conversion, r.id_devise,
            rt.lib as lib_reglement_mode, rt.abrev as abrev_reglement_mode, rt.direction as type_reglement, rd.montant as  montant_on_doc, rd.liaison_valide, rd.rapprochement_manuel, r.lib, r.abrev, r.date_saisie, r.ref_interne, r.ref_externe,
            SUM(rd2.montant)montant_on_all_doc, r.infos_supp, rt.ref_reglement_type,
            IFNULL(reg_use.montant_rembourse,0)>0 AS has_remboursement 
            FROM reglements r
            JOIN reglements_docs rd ON r.id_reglement = rd.id_reglement AND rd.ref_doc = ?
            LEFT JOIN reglements_types rt ON rt.id_reglement_type = r.id_reglement_type
            LEFT JOIN reglements_docs rd2 ON rd.id_reglement = rd2.id_reglement AND rd2.liaison_valide
            LEFT JOIN (
                SELECT rr.id_reglement_initial,SUM(r2.montant_reglement) montant_rembourse
                FROM reglements_remboursements rr
                JOIN reglements r2 ON rr.id_reglement_remboursement = r2.id_reglement
                GROUP BY rr.id_reglement_initial
            ) reg_use ON r.id_reglement=reg_use.id_reglement_initial            
            GROUP BY r.id_reglement
            ORDER BY rd.rapprochement_manuel DESC, rd.id_reglement_doc ASC,r.date_reglement ASC";
        return $this->db->run($query, [$ref_doc]);
    }

    public function update_montant_reglement_doc($montant, $id_reglement, $ref_doc) {
        $query = "UPDATE reglements_docs
                SET montant = ?
                WHERE id_reglement = ? AND ref_doc = ? AND liaison_valide = 1";
        return $this->db->run($query, [$montant, $id_reglement, $ref_doc]);
    }

    public function calcul_montant_to_pay($ref_doc) {
        $query = "SELECT SUM(rd.montant) as montant_reglements, rt.direction
            FROM reglements_docs rd
            LEFT JOIN reglements r ON r.id_reglement = rd.id_reglement
            LEFT JOIN reglements_types rt ON rt.id_reglement_type = r.id_reglement_type
            WHERE rd.ref_doc = ? AND r.valide = 1
            GROUP BY rt.direction";
        return $this->db->run($query, [$ref_doc]);
    }

    public function getInfos_reglements_docs($ref_doc, $id_reglement) {
        $query = "SELECT *
                FROM reglements_docs
                WHERE ref_doc = ? AND id_reglement = ?";
        return $this->db->run($query, [$ref_doc, $id_reglement]);
    }

    public function insert_reglement_doc($datas, $auto_update = []) {
        return $this->db->insert("reglements_docs", $datas, $auto_update);
    }

    public function update_date_maj_reglement($ref_reglement) {
        $now = DB::now();
        $query = "UPDATE reglements 
                SET date_maj = {$now}
                WHERE ref_reglement = ?";
        return $this->db->run($query, [$ref_reglement]);
    }

    public function lockReglementsDocs($ref_doc) {
        $query = "UPDATE reglements_docs SET rapprochement_manuel = 1 
                WHERE ref_doc = ?";
        return $this->db->run($query, [$ref_doc]);
    }

    public function getFacToPay($id_contact, $ignore_ref_doc = false) {
        $params = [$id_contact];
        $where = " ";
        if ($ignore_ref_doc) {
            $where = " AND d.ref_doc != ?";
            $params[] = $ignore_ref_doc;
        }
        $query = "SELECT d.ref_doc, d.id_etat_doc, de.lib_etat_doc, d.date_creation_doc date_creation, dv.sigle, dv.taux_change, df.date_echeance, d.id_devise, d.taux_conversion,
                SUM(dl.montant_ttc) as montant_ttc,SUM(dl.montant_ttc_devise) as montant_ttc_devise,
                ( SELECT SUM(montant)
                FROM reglements_docs rd
                LEFT JOIN reglements r ON r.id_reglement = rd.id_reglement
                WHERE d.ref_doc = rd.ref_doc AND r.valide = 1
            ) as montant_reglements, df.date_last_relance
            FROM documents d
            LEFT JOIN doc_fac df ON d.ref_doc = df.ref_doc
            LEFT JOIN documents_etats de ON d.id_etat_doc = de.id_etat_doc
            LEFT JOIN devises dv ON d.id_devise = dv.id_devise
            LEFT JOIN docs_lines_etendues dl ON d.ref_doc = dl.ref_doc
            WHERE d.id_contact = ? {$where} AND (d.id_etat_doc = 16 OR d.id_etat_doc = 18)
            GROUP BY d.ref_doc
            HAVING montant_ttc >= 0
            ORDER BY date_creation ASC";
        return $this->db->run($query, $params);
    }

    public function getFafToPay($id_contact, $ignore_ref_doc = false) {
        $params = [$id_contact];
        $where = " ";
        if ($ignore_ref_doc) {
            $where = " AND d.ref_doc != ?";
            $params[] = $ignore_ref_doc;
        }
        $query = "SELECT d.ref_doc, d.id_etat_doc, de.lib_etat_doc, d.date_creation_doc date_creation, d.id_devise, dv.sigle, dv.taux_change, d.taux_conversion,
                SUM(dl.montant_ttc) as montant_ttc,SUM(dl.montant_ttc_devise) as montant_ttc_devise,
                ( SELECT SUM(montant)
                FROM reglements_docs rd
                LEFT JOIN reglements r ON r.id_reglement = rd.id_reglement
                WHERE d.ref_doc = rd.ref_doc AND r.valide = 1
            ) as montant_reglements, df.date_last_relance
            FROM documents d
            LEFT JOIN doc_fac df ON d.ref_doc = df.ref_doc
            LEFT JOIN devises dv ON d.id_devise = dv.id_devise
            LEFT JOIN documents_etats de ON d.id_etat_doc = de.id_etat_doc
            LEFT JOIN docs_lines_etendues dl ON d.ref_doc = dl.ref_doc
            WHERE d.id_contact = ? {$where} AND (d.id_etat_doc = 32 OR d.id_etat_doc = 34)
            GROUP BY d.ref_doc
            HAVING montant_ttc >= 0
            ORDER BY date_creation ASC";
        return $this->db->run($query, $params);
    }

    public function getAvoirsRgmt($type, $id_contact) {
        // TODO refactoring
        $query = "SELECT r.ref_reglement, r.id_reglement, IF(r.montant_reglement-IFNULL(reg_use.montant_rembourse,0)>=0,r.montant_reglement-IFNULL(reg_use.montant_rembourse,0),0) montant_reglement, r.date_reglement, rt.lib as lib_reglement_mode, dv.sigle, dv.taux_change, rd.liaison_valide, rt.direction as type_reglement,
            r.id_devise, r.taux_conversion, rt.id_reglement_type as id_reglement_mode, SUM(rd.montant) as montant_used
            FROM reglements r
            LEFT JOIN reglements_docs rd ON r.id_reglement = rd.id_reglement AND (rd.liaison_valide = 1 OR ISNULL(rd.liaison_valide))
            LEFT JOIN reglements_types rt ON rt.id_reglement_type = r.id_reglement_type
            LEFT JOIN devises dv ON r.id_devise = dv.id_devise
            LEFT JOIN (
                SELECT rr.id_reglement_initial,SUM(r2.montant_reglement) montant_rembourse
                FROM reglements_remboursements rr
                JOIN reglements r2 ON rr.id_reglement_remboursement = r2.id_reglement
                GROUP BY rr.id_reglement_initial
            ) reg_use ON r.id_reglement=reg_use.id_reglement_initial
            WHERE r.id_contact = ? AND r.valide = 1 AND rt.direction = ?
            GROUP BY r.id_reglement
            HAVING ABS(ABS(montant_reglement) - ABS(montant_used)) > 0.02 OR ISNULL(montant_used)
            ORDER BY r.date_reglement ASC";
        return $this->db->run($query, [$id_contact, $type]);
    }
    public function getAllRemboursement(){
        $query = "SELECT id_reglement_remboursement 
        FROM `reglements_remboursements`";
        $res = $this->db->run($query)->fetchAll(PDO::FETCH_COLUMN);
        return $res;
    }
    public function getId_reglement_impaye($id_reglement) {
        $query = "SELECT id_impaye id 
                    FROM reglements_impayes 
                    WHERE id_reglement_initial = ? OR id_reglement_impaye = ?";
        return $this->db->run($query, [$id_reglement, $id_reglement]);
    }

    public function getAvoirsFac($id_contact, $ref_doc_ignore) {
        $q_where = " ";
        $params = [$id_contact];
        if ($ref_doc_ignore) {
            $q_where = " AND d.ref_doc != ?";
            $params[] = $ref_doc_ignore;
        }
        $query = "SELECT d.ref_doc, d.id_etat_doc, d.date_creation_doc date_creation, dv.sigle, dv.taux_change, d.id_devise, d.taux_conversion,
            SUM(dl.montant_ttc) as montant_ttc,
            ( SELECT SUM(montant)
            FROM reglements_docs rd
            LEFT JOIN reglements r ON r.id_reglement = rd.id_reglement
            WHERE d.ref_doc = rd.ref_doc AND r.valide = 1
        ) as montant_reglements
        FROM documents d
        LEFT JOIN devises dv ON d.id_devise = dv.id_devise
        LEFT JOIN doc_fac df ON d.ref_doc = df.ref_doc
        LEFT JOIN docs_lines_etendues dl ON d.ref_doc = dl.ref_doc
        WHERE d.id_etat_doc = 18 AND d.id_contact = ? {$q_where}
        GROUP BY d.ref_doc
        HAVING montant_ttc < 0
        ORDER BY date_creation ASC";
        return $this->db->run($query, $params);
    }

    public function getAvoirsFaf($id_contact, $ref_doc_ignore) {
        $q_where = " ";
        $params = [$id_contact];
        if ($ref_doc_ignore) {
            $q_where = " AND d.ref_doc != ?";
            $params[] = $ref_doc_ignore;
        }
        $query = "SELECT d.ref_doc, d.id_etat_doc, d.date_creation_doc date_creation, d.id_devise, d.taux_conversion,
            SUM(dl.montant_ttc) as montant_ttc,
            ( SELECT SUM(montant)
            FROM reglements_docs rd
            LEFT JOIN reglements r ON r.id_reglement = rd.id_reglement
            WHERE d.ref_doc = rd.ref_doc AND r.valide = 1
        ) as montant_reglements
        FROM documents d
        LEFT JOIN docs_lines_etendues dl ON d.ref_doc = dl.ref_doc
        WHERE d.id_etat_doc = 34 AND d.id_contact = ? {$q_where}
        GROUP BY d.ref_doc
        HAVING montant_ttc < 0
        ORDER BY date_creation ASC";
        return $this->db->run($query, $params);
    }

    public function get_infos_facturation($id_contact, $ref_doc) {
        $query = "SELECT d.ref_doc, d.id_etat_doc, de.lib_etat_doc, d.date_creation_doc date_creation, dv.sigle, dv.taux_change, d.id_devise, d.taux_conversion,
                    SUM(dl.montant_ttc) as montant_ttc,
                    ( SELECT SUM(montant)
                    FROM reglements_docs rd
                    LEFT JOIN reglements r ON r.id_reglement = rd.id_reglement
                    WHERE d.ref_doc = rd.ref_doc AND r.valide = 1
                ) as montant_reglements
                FROM documents d
                LEFT JOIN devises dv ON d.id_devise = dv.id_devise
                LEFT JOIN doc_cdc df ON d.ref_doc = df.ref_doc
                LEFT JOIN documents_etats de ON d.id_etat_doc = de.id_etat_doc
                LEFT JOIN docs_lines_etendues dl ON d.ref_doc = dl.ref_doc
                WHERE d.id_contact = ? AND d.ref_doc != ? 
                AND (d.id_etat_doc = 8 OR d.id_etat_doc = 9 OR d.id_etat_doc = 74)
                GROUP BY d.ref_doc
                HAVING montant_ttc >= 0
                ORDER BY date_creation ASC";
        return $this->db->run($query, [$id_contact, $ref_doc]);
    }

    public function maj_date_reglement($ref_reglement) {
        $now = DB::now();
        $query = "UPDATE reglements SET date_maj = {$now}  
                  WHERE ref_reglement = ?  ";
        return $this->db->run($query, [$ref_reglement]);
    }

    public function maj_etat_reglement($liaison, $ref_doc) {
        $query = "UPDATE reglements_docs SET liaison_valide = ?
                  WHERE ref_doc = ?";
        return $this->db->run($query, [$liaison, $ref_doc]);
    }

    public function getMontant_ttc_light($ref_doc) {
        $query = "SELECT SUM(dl.montant_ttc) as montant_ttc
            FROM docs_lines_etendues dl
            WHERE dl.ref_doc = ?";
        return $this->db->run($query, [$ref_doc]);
    }

    public function getMontant_ht_light($ref_doc) {
        $query = "SELECT SUM(dl.montant_ht) as montant_ht
            FROM docs_lines_etendues dl
            WHERE dl.ref_doc = ?";
        return $this->db->run($query, [$ref_doc]);
    }

    public function getCa_ttc_light($ref_doc) {
        $query = "SELECT SUM(dl.montant_ttc) as montant_ttc
            FROM docs_lines_etendues dl
            WHERE dl.ref_doc = ? AND dl.is_chiffre_affaires = 1";
        return $this->db->run($query, [$ref_doc]);
    }

    public function getCa_ht_light($ref_doc) {
        $query = "SELECT SUM(dl.montant_ht) as montant_ht
            FROM docs_lines_etendues dl
            WHERE dl.ref_doc = ? AND dl.is_chiffre_affaires = 1";
        return $this->db->run($query, [$ref_doc]);
    }

    public function getDocumentTypes($with_id = false) {
        $select = "*";
        if ($with_id) {
            $select = " id_type_groupe, id_type_doc ";
        }
        $query = " SELECT {$select} FROM documents_types";
        return $this->db->run($query, []);
    }

    public function getDocumentTypesGroups() {
      $query = "SELECT * FROM documents_types_groupes";
      return $this->db->run($query, []);
    }

    public function getLivraison_line($ref_doc, $ref_art_categ) {
        $query = "SELECT dl.*
            FROM docs_lines_etendues_full dl
            LEFT JOIN articles a ON a.ref_article = dl.ref_article
            LEFT JOIN art_categs ac ON a.id_art_categ = ac.id_art_categ
            WHERE ref_doc = ? AND 
            (ac.ref_art_categ = ? OR a.id_modele_spe = 3)";
        return $this->db->run($query, [$ref_doc, $ref_art_categ]);
    }

    public function getNb_lignes_liees($id_doc_line_parent, $ref_doc) {
        $query = "SELECT COUNT('ref_doc_line') AS nb
            FROM docs_lines
            WHERE id_doc_line_parent = ?
            AND ref_doc = ?";
        return $this->db->run($query, [$id_doc_line_parent, $ref_doc]);
    }

    public function majId_pdf_template($id_pdf_template, $ref_doc) {
        $query = "UPDATE documents
            SET id_pdf_template = ?
            WHERE ref_doc = ?";
        return $this->db->run($query, [$id_pdf_template, $ref_doc]);
    }

    public function getAbrev_reglement_mode() {
        $query = "SELECT abrev as abrev_reglement_mode FROM reglements_modes";
        return $this->db->run($query);
    }

    public function delete_line_parent($id_doc_line_parent) {
        $query = "DELETE FROM docs_lines WHERE id_doc_line_parent = ?";
        return $this->db->run($query, [$id_doc_line_parent]);
    }

    public function get_composition_fab($numero_serie) {
        $query = "SELECT articles.lib_article,docs_lines.ref_article, docs_lines.qte, docs_lines.pu_ht, docs_lines.remise, docs_lines.tva,doc_fab.qte_fab
            FROM doc_fab_sn
            INNER JOIN doc_fab ON doc_fab.ref_doc = doc_fab_sn.ref_doc
            INNER JOIN docs_lines_etendues_full AS docs_lines ON docs_lines.ref_doc = doc_fab.ref_doc
            INNER JOIN articles ON articles.ref_article = docs_lines.ref_article
            WHERE doc_fab_sn.numero_serie = ?
            ORDER BY docs_lines.ordre";
        return $this->db->run($query, [$numero_serie]);
    }

    public function get_id_line_enfant($id_doc_line_parent) {
        $query = "SELECT  id_doc_line , lib_article , qte  
                  FROM  docs_lines  WHERE  id_doc_line_parent = ? ";
        return $this->db->run($query, [$id_doc_line_parent]);
    }

    public function getRef_doc_by_uuid($uuid_lmb) {
        $query = "SELECT ref_doc FROM documents WHERE uuid_lm = ?";
        return $this->db->run($query, [$uuid_lmb]);
    }

    public function getRef_doc_by_code_barre($code_barre, $id_type_doc = false) {
        $where = " ";
        $params[] = $code_barre;
        if ($id_type_doc) {
            $where = " AND id_type_doc = ?";
            $params[] = $id_type_doc;
        }
        $query = "SELECT ref_doc FROM documents WHERE code_barre = ? {$where}";
        return $this->db->run($query, $params);
    }

    public function charge_defaut_liste_factures_a_regler_modele_pdf() {
        $query = "SELECT pm.code_pdf_modele
            FROM pdf_modeles_usage pmu
            LEFT JOIN pdf_modeles pm ON pmu.id_pdf_modele = pm.id_pdf_modele
            WHERE pmu.usage = 'defaut' AND id_objet = 9";
        return $this->db->run($query);
    }

    public function charge_liste_factures_a_regler_modele_pdf() {
        $query = "SELECT id_pdf_modele, id_pdf_type, lib_modele, desc_modele , code_pdf_modele
            FROM pdf_modeles
            WHERE id_pdf_type = '9' ";
        return $this->db->run($query);
    }

    public function getRef_doc_by_ref_externe($ref_doc_externe, $id_type_doc = false) {
        $where = " ";
        $params[] = $ref_doc_externe;
        if ($id_type_doc) {
            $where = " AND id_type_doc = ?";
            $params[] = $id_type_doc;
        }
        $query = "SELECT ref_doc FROM documents WHERE ref_doc_externe = ? {$where}";
        return $this->db->run($query, $params);
    }

    public function getQteFromRef_doc_line($ref_doc_line) {
        $query = "SELECT qte FROM docs_lines
                WHERE ref_doc_line = ?";
        return $this->db->run($query, [$ref_doc_line]);
    }

    public function getDoc_line($ref_doc_line) {
        $query = "SELECT dle.id_doc_line, ref_doc_line, uuid_lm, ref_doc, ref_article, lib_article, desc_article,
            qte, pu_ht, remise, tva, ordre, id_doc_line_parent,numero_serie,
            visible, pa_ht, pa_forced, fd_ht, fd_forced,montant_ht,montant_ttc, 
            details_qte, qte_retour, details_qte_retour
            FROM docs_lines_etendues_full dle
            LEFT JOIN docs_lines_sn dls ON dls.id_doc_line = dle.id_doc_line
            WHERE ref_doc_line = ?";
        return $this->db->run($query, [$ref_doc_line]);
    }

    public function getNbLinesWithoutSN($id_doc_line) {
        $query = "SELECT SUM(sn_qte) as nb
            FROM docs_lines_sn
            WHERE id_doc_line = ? AND numero_serie IS NOT NULL
            GROUP BY id_doc_line";
        return $this->db->run($query, [$id_doc_line]);
    }

    public function setSNLines($numero_serie, $id_doc_line) {
        $query = "UPDATE docs_lines_sn
            SET numero_serie = ?, sn_qte = '1'
            WHERE id_doc_line = ? AND numero_serie IS NULL LIMIT 1";
        return $this->db->run($query, [$numero_serie, $id_doc_line]);
    }

    public function getDoc_from_refLine($ref_doc_line) {
        $query = "SELECT ref_doc FROM docs_lines WHERE ref_doc_line = ?";
        return $this->db->run($query, [$ref_doc_line]);
    }

    public function getDoc_from_IdLine($id_doc_line) {
        $query = "SELECT ref_doc FROM docs_lines WHERE id_doc_line = ?";
        return $this->db->run($query, [$id_doc_line]);
    }

    public function get_commandes_validees() {
        $query = "
                SELECT
                    d.ref_doc,d.id_contact,d.date_creation_doc,de.lib_etat_doc, d.nom_contact, sum( dle.net_ht ) AS total_cdc, ljdvc.gc_nom
                FROM
                    documents d
                    INNER JOIN docs_lines_etendues dle ON d.ref_doc = dle.ref_doc
                    INNER JOIN documents_etats de on de.id_etat_doc=d.id_etat_doc
                    LEFT JOIN (select dvc.ref_doc, GROUP_CONCAT(a.nom_complet) as gc_nom
                                FROM doc_ventes_commerciaux dvc
                                INNER JOIN contacts a on a.id_contact=dvc.id_contact
                                GROUP BY dvc.ref_doc) ljdvc on ljdvc.ref_doc=d.ref_doc
                WHERE
                    d.ref_doc LIKE 'CDC%'
                    AND d.date_creation_doc BETWEEN DATE_SUB( CURRENT_TIMESTAMP, interval 1 day) and CURRENT_TIMESTAMP
                GROUP BY
                    d.ref_doc
                ORDER BY d.date_creation_doc DESC LIMIT 0,50 ";
        return $this->db->run($query);
    }

    public function get_documents_en_saisie() {
        $query = "
                SELECT
                    d.ref_doc,de.date_event,a.nom_complet AS nom,a.id_contact,c.email
                FROM
                    documents_events de
                    INNER JOIN documents d ON d.ref_doc = de.ref_doc
                    INNER JOIN users u on u.ref_user=de.ref_user
                    INNER JOIN contacts a on a.id_contact=u.id_contact
                    INNER JOIN coordonnees c on c.id_coord=u.id_coord_user
                WHERE
                    d.date_creation_doc BETWEEN DATE_SUB( CURRENT_TIMESTAMP, interval 1 day) and CURRENT_TIMESTAMP
                    AND (d.id_etat_doc=1 OR d.id_etat_doc=6)
                GROUP BY
                    d.ref_doc ";
        return $this->db->run($query);
    }

    public function getDateLastEvent($ref_doc, $id_event_type) {
        $query = "SELECT MAX(date_event) d FROM documents_events
                WHERE ref_doc = ? AND id_event_type = ?";
        return $this->db->run($query, [$ref_doc, $id_event_type]);
    }

    public function _Set_property($name, $value, $ref_doc) {
        $query = "UPDATE document SET {$name} = ? 
                  WHERE ref_doc = ?";
        return $this->db->run($query, [$value, $ref_doc]);
    }

    public function getAllEtats($id_type_doc) {
        $query = "SELECT dt.lib_type_doc, de.*
                FROM documents_etats de
                JOIN documents_types dt ON dt.id_type_doc = de.id_type_doc
                WHERE dt.id_type_doc = ? ORDER BY ordre ";
        return $this->db->run($query, [$id_type_doc]);
    }

    public function getFacFromPeriodeAndMagasin($date_debut, $date_fin) {
        $server_tz_offset = lmbDateInter::getServerTZOffset(true);
        $entreprise_tz_offset = lmbDateInter::getEntrepriseTZOffset(true);

        $query = "SELECT id_magasin,SUBSTR(CONVERT_TZ(d.date_creation_doc, {$server_tz_offset}, {$entreprise_tz_offset}),1,10) periode,
            SUM(dle.montant_ttc) ca_ttc, COUNT(DISTINCT df.ref_doc) nb_doc, (SUM(dle.montant_ttc) / COUNT(DISTINCT df.ref_doc)) AS tic_moyen
            FROM doc_fac df
            JOIN documents d ON d.ref_doc = df.ref_doc
            JOIN docs_lines_etendues dle ON dle.ref_doc = df.ref_doc
            WHERE d.id_type_doc = 4 AND d.id_etat_doc IN (18,19) 
              AND d.date_creation_doc >= ? AND d.date_creation_doc <= ?
            GROUP BY id_magasin, periode WITH ROLLUP;";
        return $this->db->run($query, [$date_debut, $date_fin]);
    }

    public function get_lignes_from_ref_article($ref_article, $ref_doc) {
        $query = "
                SELECT dl.ref_doc_line
                FROM docs_lines dl
                JOIN articles a on dl.ref_article = a.ref_article AND dl.ref_article = ?
                WHERE dl.ref_doc = ?";
        return $this->db->run($query, [$ref_article, $ref_doc]);
    }

    public function delete_ligne_remise_global($ref_doc, $ref_article) {
        global $REF_ARTICLE_ESCOMPTE;
        $query = "DELETE FROM docs_lines 
                WHERE ref_doc = ? AND (ref_article = ? OR ref_article = ?)";
        return $this->db->run($query, [$ref_doc, $ref_article, $REF_ARTICLE_ESCOMPTE]);
    }

    public function saveDetailsRemiseGlobaleBDD($details_remise_globale, $ref_doc) {
        $query = "UPDATE documents 
            SET details_remise_globale = ?
            WHERE ref_doc = ?";
        return $this->db->run($query, [$details_remise_globale, $ref_doc]);
    }


    public function update_date_modif($ref_doc) {
        $now = DB::now();
        $query = "UPDATE documents SET date_modif = {$now}
                WHERE ref_doc = ?";
        return $this->db->run($query, [$ref_doc]);
    }

    public function createDocsLinesEtenduesFull($selectDocsLinesEtendues) {
        $this->db->run("DROP VIEW IF EXISTS docs_lines_etendues_full");
        $query = "CREATE ALGORITHM=MERGE
            VIEW docs_lines_etendues_full AS {$selectDocsLinesEtendues}";
        return $this->db->run($query);
    }

    public function createDocsLinesEtendues($selectDocsLinesEtendues) {
        $this->db->run("DROP VIEW IF EXISTS docs_lines_etendues");
        $query = "CREATE ALGORITHM=MERGE
            VIEW docs_lines_etendues AS {$selectDocsLinesEtendues}
            WHERE dl.visible = 1 AND dl.id_doc_line_parent IS NULL";
        return $this->db->run($query);
    }

    public function getUsablePdfTemplates($id_type_doc) {
        $type_doc = PdfTemplate::getTemplateTypeByTypeDoc($id_type_doc);
        if (!is_array($type_doc)) {
            $type_doc = [$type_doc];
        }
        $q_in = implode(',', array_fill(0, count($type_doc), '?'));
        $query = "SELECT pt.id_pdf_template, pt.lib, 
                 CASE
                    WHEN pt.favori = 1 THEN 'defaut'
                    WHEN pt.actif = 1 THEN 'actif'
                    ELSE 'inactif'
                 END \"usage\"
                FROM pdf_template pt 
                WHERE pt.pdf_template_type IN ({$q_in}) AND pt.actif = 1 
                ORDER BY favori DESC";
        return $this->db->run($query, $type_doc);
    }

    public function getLast_ref_doc_by_type($id_type_doc) {
        $query = "SELECT ref_doc 
                  FROM documents 
                  WHERE id_type_doc = ?
                  ORDER BY date_creation_doc DESC limit 1";
        return $this->db->run($query, [$id_type_doc]);
    }

    public function toggleInsert_description($code_doc, $value) {
        $query = "UPDATE documents_types 
                 SET insert_description = ?
                 WHERE code_doc = ?";
        return $this->db->run($query, [$value, $code_doc]);
    }

    public function getRef_user_create($ref_doc) {
        $query = "SELECT ref_user FROM documents_events 
                  WHERE id_event_type = 1 AND ref_doc = ?";
        return $this->db->run($query, [$ref_doc]);
    }

    public function getIds_doc_lines_for_ref_article($ref_doc, $ref_article, $qte = false) {
        $params = [$ref_doc, $ref_article];
        $query = "SELECT id_doc_line FROM docs_lines
                WHERE ref_doc = ? AND ref_article = ? ";
        if ($qte) {
            $query .= " AND qte = ?";
            $params[] = $qte;
        }
        return $this->db->run($query, $params);
    }

    public function getIds_doc_for_ref_article($ref_article, $by_id = false) {
        $field = $by_id ? " id_article " : " dl.ref_article";
        if (!is_array($ref_article)) {
            $ref_article = [$ref_article];
        }

        $q_in = implode(',', array_fill(0, count($ref_article), '?'));
        $query = "SELECT dl.ref_doc 
                FROM docs_lines dl
            INNER JOIN documents d ON d.ref_doc = dl.ref_doc
            INNER JOIN articles a ON a.ref_article = dl.ref_article
            WHERE id_etat_doc IN (SELECT id_etat_doc FROM documents_etats WHERE is_open AND id_type_doc IN (3, 7, 9, 11))
                AND {$field} IN ({$q_in})
            GROUP BY dl.ref_doc";
        return $this->db->run($query, $ref_article);
    }

    public function getValue_doc_carac($ref_doc, $id_carac) {
        $query = "SELECT value 
                FROM docs_caracs 
                WHERE ref_doc = ?
                AND id_carac = ?";
        return $this->db->run($query, [$ref_doc, $id_carac]);
    }

    public function insert_carac_message($data) {
        return $this->db->insert("caracs_messages", $data);
    }

    public function update_carac_message($data, $where) {
        return $this->db->update("caracs_messages", $data, $where);
    }

    public function insert_doc_carac($data, $auto_update = []) {
        return $this->db->insert("docs_caracs", $data, $auto_update);
    }

    public function delete_doc_carac(string $ref_doc, int $id_carac)
    {
        $query = "
            DELETE FROM docs_caracs 
            WHERE ref_doc = :ref_doc AND id_carac = :id_carac
        ";

        return $this->db->run($query, ["ref_doc" => $ref_doc, "id_carac" => $id_carac]);
    }

    public function get_carac_value($ref_doc, $id_carac) {
        $query = 'SELECT dc.value, c.id_carac_type
                FROM docs_caracs dc 
                LEFT JOIN caracs c ON c.id_carac = dc.id_carac
                WHERE dc.ref_doc = ?
                AND dc.id_carac = ?';
        return $this->db->run($query, [$ref_doc, $id_carac]);
    }

    public function getMessage_value($id_message) {
        $query = "SELECT message_value FROM caracs_messages WHERE id_message = ?";
        return $this->db->run($query, [$id_message]);
    }

    public function setUuid_lm($uuid_lm, $ref_doc) {
        $query = "UPDATE documents SET uuid_lm = ? 
                WHERE ref_doc = ?";
        return $this->db->run($query, [$uuid_lm, $ref_doc]);
    }

    public function setRef_doc_interne($ref_doc_interne, $ref_doc) {
        $query = "UPDATE documents SET ref_doc_interne = ? 
                WHERE ref_doc = ?";
        return $this->db->run($query, [$ref_doc_interne, $ref_doc]);
    }

    public function update_id_magasin($id_magasin, $ref_doc, $table) {
        $query = "UPDATE {$table}
            SET id_magasin = ?
            WHERE ref_doc = ?";
        return $this->db->run($query, [$id_magasin, $ref_doc]);
    }

    public function getQteTotaleArticle($ref_doc, $ref_article) {
        $query = "SELECT SUM(qte) FROM docs_lines 
                WHERE ref_doc = ? AND ref_article = ?";
        return $this->db->run($query, [$ref_doc, $ref_article]);
    }

    public function getMontantRetour($ref_doc) {
        $query = "SELECT SUM(pu_ttc_devise) as montant 
                  FROM docs_lines WHERE qte < 0 AND ref_doc = ?";
        return $this->db->run($query, [$ref_doc]);
    }

    public function getCaracs_doc($ref_doc) {
        $query = "SELECT id_carac, value 
                  FROM docs_caracs WHERE ref_doc = ?";
        return $this->db->run($query, [$ref_doc]);
    }

    public function getCAFromPeriode($search, $granularite = 'mois', $use_hours = false) {
        $params = [];
        $query_where = " dle.visible = 1 AND d.id_type_doc IN (4,15) AND d.id_etat_doc IN(18,19,62) AND dlink.ref_doc_source IS NULL AND dlink2.ref_doc_destination IS NULL ";
        $where_magasin = " ";
        if (!empty($search['id_magasin'])) {
            if (!is_array($search['id_magasin'])) {
                $search['id_magasin'] = [$search['id_magasin']];
            }
            $q_in_magasin = implode(',', array_fill(0, count($search['id_magasin']), '?'));
            $where_magasin = "
                LEFT JOIN doc_fac lt ON d.ref_doc=lt.ref_doc
                LEFT JOIN doc_tic lt2 ON d.ref_doc=lt2.ref_doc
            ";
            $query_where .= " AND (lt.id_magasin IN ($q_in_magasin) OR lt2.id_magasin IN ($q_in_magasin)) ";
            $params = array_merge($search['id_magasin'],$search['id_magasin']);
        }
        else{
            $where_magasin = "
                LEFT JOIN doc_fac lt ON d.ref_doc=lt.ref_doc
                LEFT JOIN doc_tic lt2 ON d.ref_doc=lt2.ref_doc
            ";
        }

        
        if (!empty($search['id_contact'])) {
            $query_where .= " AND d.id_contact = ? ";
            $params[] = $search['id_contact'];
        }
        if (!empty($search['date_debut'])) {
            if ($use_hours) {
                $date_debut = lmbDateInter::DisplayToServerFormat($search['date_debut']);
            } else {
                $date_debut = lmbDateInter::ConvertServertoEntrepriseBeginOfDay($search['date_debut']);
            }
            $query_where .= " AND d.date_creation_doc >=  ?";
            $params[] = $date_debut;
        }
        if (!empty($search['date_fin'])) {
            if ($use_hours) {
                $date_fin = lmbDateInter::DisplayToServerFormat($search['date_fin']);
            } else {
                $date_fin = lmbDateInter::ConvertServertoEntrepriseEndOfDay($search['date_fin']);
            }
            $query_where .= " AND d.date_creation_doc <= ?";
            $params[] = $date_fin;
        }

        if (!empty($search['id_caisse'])) {
            if ($search['id_caisse'] == 'NULL') {
                $query_where .= " AND d.id_caisse IS NULL";
            } else {
                if (!is_array($search['id_caisse'])) {
                    $search['id_caisse'] = [$search['id_caisse']];
                }
                $q_in = implode(',', array_fill(0, count($search['id_caisse']), '?'));
                $query_where .= " AND d.id_caisse IN (" . $q_in . ")";
                $params = array_merge($params, $search['id_caisse']);
            }
        }
    
        $periode = "";
        $group = "";
    
        if ($granularite) {
            switch ($granularite) {
                case 'heure':
                    $periode = "DATE_FORMAT(d.date_creation_doc,'%Y-%m-%d-%H') periode,";
                    break;
                case 'jour':
                    $periode = "DATE_FORMAT(d.date_creation_doc,'%Y-%m-%d') periode,";
                    break;
                case 'semaine':
                    $periode = "DATE_FORMAT(d.date_creation_doc,'%Y-%W') periode,";
                    break;
                case 'mois':
                    $periode = "DATE_FORMAT(d.date_creation_doc,'%Y-%m') periode,";
                    break;
                default:
                    break;
            }
            $group = "GROUP BY periode";
        }
    
        $query = "
        SELECT {$periode} 
               SUM(CASE WHEN dle.is_chiffre_affaires = 1 THEN dle.montant_ht ELSE 0 END) ht, 
               SUM(CASE WHEN dle.is_chiffre_affaires = 1 THEN dle.montant_tva ELSE 0 END) tva, 
               SUM(CASE WHEN dle.is_chiffre_affaires = 1 THEN dle.montant_ttc ELSE 0 END) ttc, 
               SUM(CASE WHEN dle.is_chiffre_affaires = 1 THEN dle.marge_nette_devise * dle.qte ELSE 0 END) marge_nette, 
               SUM(IF(dle.montant_ttc > 0, dle.montant_ttc, 0)) ttc_pos, 
               SUM(IF(dle.montant_ht < 0, dle.montant_ht, 0)) ht_neg, 
               SUM(IF(dle.montant_ttc < 0, dle.montant_ttc, 0)) ttc_neg, 
               COUNT(DISTINCT dle.ref_doc) nb_ventes
        FROM docs_lines_etendues dle
            INNER JOIN documents d ON d.ref_doc = dle.ref_doc
            LEFT JOIN documents_liaisons dlink ON d.ref_doc=dlink.ref_doc_destination AND dlink.ref_doc_source LIKE 'TIC-%'
            LEFT JOIN documents_liaisons dlink2 ON d.ref_doc=dlink2.ref_doc_source AND dlink2.ref_doc_destination LIKE 'TIC-%'
        {$where_magasin}
        WHERE {$query_where}
        {$group}";
    
        return $this->db->run($query, $params);
    }

    public function getTVAFromPeriode($search, $use_hours = false) {
        $query_where = "dle.visible = 1 AND d.id_type_doc IN (4,15) AND d.id_etat_doc NOT IN(16,17,59,60)
        AND (dl1.ref_doc_destination IS NULL OR (d.id_type_doc = 15 AND dl1.ref_doc_destination NOT LIKE 'FAC-%') OR d.id_type_doc = 4)  
        AND (dl2.ref_doc_destination IS NULL OR (d.id_type_doc = 15 AND dl2.ref_doc_destination NOT LIKE 'FAC-%') OR d.id_type_doc = 4)
        ";
        $params = [];

        $where_magasin = " ";
        if (!empty($search['id_magasin'])) {
            if (!is_array($search['id_magasin'])) {
                $search['id_magasin'] = [$search['id_magasin']];
            }
            $q_in_magasin = implode(',', array_fill(0, count($search['id_magasin']), '?'));
            $where_magasin = "JOIN doc_fac lt ON d.ref_doc=lt.ref_doc AND lt.id_magasin IN (" . $q_in_magasin . ")";
            $params = $search['id_magasin'];
        }
        
        if (!empty($search['id_contact'])) {
            $query_where .= " AND d.id_contact = ? ";
            $params[] = $search['id_contact'];
        }
        if (!empty($search['id_caisse'])) {
            if ($search['id_caisse'] == 'NULL') {
                $query_where .= " AND d.id_caisse IS NULL ";
            } else {
                if (!is_array($search['id_caisse'])) {
                    $search['id_caisse'] = [$search['id_caisse']];
                }
                $q_in = implode(',', array_fill(0, count($search['id_caisse']), '?'));
                $query_where .= " AND d.id_caisse IN (" . $q_in . ") ";
                $params = array_merge($params, $search['id_caisse']);
            }
        }

        if (!empty($search['date_debut'])) {
            if ($use_hours) {
                $date_debut = lmbDateInter::DisplayToServerFormat($search['date_debut']);
            } else {
                $date_debut = lmbDateInter::ConvertServertoEntrepriseBeginOfDay($search['date_debut']);
            }
            $query_where .= " AND d.date_creation_doc >=  ?";
            $params[] = $date_debut;
        }
        if (!empty($search['date_fin'])) {
            if ($use_hours) {
                $date_fin = lmbDateInter::DisplayToServerFormat($search['date_fin']);
            } else {
                $date_fin = lmbDateInter::ConvertServertoEntrepriseEndOfDay($search['date_fin']);
            }
            $query_where .= " AND d.date_creation_doc <= ?";
            $params[] = $date_fin;
        }

        $query = "SELECT dle.id_tva,IFNULL(dle.tva_taux,0) tva_taux,dle.tva_type,dle.tva, sum(dle.montant_tva) montant_tva, sum(dle.montant_ht) montant_ht, sum(dle.montant_ttc) montant_ttc
            FROM docs_lines_etendues dle
            INNER JOIN documents d ON d.ref_doc = dle.ref_doc
            LEFT JOIN documents_liaisons dl1 ON dl1.ref_doc_source = d.ref_doc AND dl1.active = 1
            LEFT JOIN documents_liaisons dl2 ON dl1.ref_doc_destination = dl2.ref_doc_source AND dl2.active = 1
            {$where_magasin}
            WHERE $query_where
            GROUP BY IFNULL(dle.tva_taux,0)";

        return $this->db->run($query, $params);
    }

    public function getRGTFACFromPerdiode($search, $docs, $type = "entrant", $withDetails = false, $query_where = "", $link_table = 'doc_fac', $group_by_devise = false, $count_ventes = false, $use_hours = false) {
        $params = [];
        if (empty($query_where))
            $query_where = " 1=1 ";
        if ($docs && is_array($docs)){
            $q_in_type_doc = implode(',', array_fill(0, count($docs[1]), '?'));
            if (isset($search['search_for']) &&  $search['search_for'] == 'reglement') {
                $query_where .= " AND d.id_etat_doc NOT IN (" . $q_in_type_doc . ") ";
                $params = array_merge($params, $docs[1]);
            } else {
                $query_where .= " AND d.id_type_doc = ? AND d.id_etat_doc NOT IN (" . $q_in_type_doc . ") ";
                $params = array_merge($params, array_merge([$docs[0]], $docs[1]));
            }
        }
        if (!empty($search['id_contact'])) {
            $query_where .= " AND d.id_contact = ? ";
            $params[] = $search['id_contact'];
        }

        if (!empty($search['id_caisse'])) {
            if ($search['id_caisse'] == 'NULL') {
                $query_where .= " AND d.id_caisse IS NULL ";
            } else {
                if (!is_array($search['id_caisse'])) {
                    $search['id_caisse'] = [$search['id_caisse']];
                }
                $q_in_caisse = implode(',', array_fill(0, count($search['id_caisse']), '?'));
                $query_where .= " AND d.id_caisse IN (" . $q_in_caisse . ") ";
                $params = array_merge($params, $search['id_caisse']);
            }
        }

        if (!empty($search['date_debut'])) {
            if ($use_hours) {
                $date_debut = lmbDateInter::DisplayToServerFormat($search['date_debut']);
            } else {
                $date_debut = lmbDateInter::ConvertServertoEntrepriseBeginOfDay($search['date_debut']);
            }
            if (isset($search['search_for']) &&  $search['search_for'] == 'reglement') {
                $query_where .=  " AND r.date_reglement >=  ?";
            } else {
                $query_where .=  " AND d.date_creation_doc >=  ?";
            }

            $params[] = $date_debut;
        }
        if (!empty($search['date_fin'])) {
            if ($use_hours) {
                $date_fin = lmbDateInter::DisplayToServerFormat($search['date_fin']);
            } else {
                $date_fin = lmbDateInter::ConvertServertoEntrepriseEndOfDay($search['date_fin']);
            }
            if (isset($search['search_for']) &&  $search['search_for'] == 'reglement') {
                $query_where .=  " AND r.date_reglement <= ?";
            } else {
                $query_where .= " AND d.date_creation_doc <= ?";
            }

            $params[] = $date_fin;
        }

        if (
            ((empty($datedebut)) && (empty($datefin)))
            ||
            (($search['date_debut'] == "----") && ($search['date_fin'] == "----"))
        ) {
            if (!empty($search['date_exercice'])) {
                $datesplit = explode(";", $search['date_exercice']);
                $query_where .= " AND d.date_creation_doc >= ? AND d.date_creation_doc <= ? ";
                $params[] = $datesplit[0] . " 00:00:00";
                $params[] = $datesplit[1] . " 23:59:59";
            }
        }

        $join_magasin = "";
        if (!empty($search['id_magasin'])) {
            if (!is_array($search['id_magasin'])) {
                $search['id_magasin'] = [$search['id_magasin']];
            }
            $q_in_magasin = implode(',', array_fill(0, count($search['id_magasin']), '?'));
            $join_magasin = "JOIN $link_table lt ON d.ref_doc=lt.ref_doc AND lt.id_magasin IN (" . $q_in_magasin . ")";
            $params = array_merge($search['id_magasin'], $params);
        }
        $params[] = $type;

        if (!$withDetails) {
            $select_lib = "rt.lib AS lib_reglement_mode";
            $join_devise = "";
            $group_devise = "";
            $select_nb = "";
            if ($group_by_devise) {
                $select_lib = 'rt.lib AS lib_reglement_mode, r.id_devise, dev.abrev_devise, sum(rd.montant * r.taux_conversion) montant_converti';
                $join_devise = "LEFT JOIN devises dev ON r.id_devise = dev.id_devise";
                $group_devise = "r.id_devise, dev.abrev_devise, rt.lib, ";
            }
            if ($count_ventes) {
                $select_nb = ", COUNT(r.id_reglement) nb_reglements";
            }
            if (isset($search['search_for']) &&  $search['search_for'] == 'reglement') {
                $join_documents_liaisons = '';
            } else {
                $join_documents_liaisons = "LEFT JOIN documents_liaisons dli ON d.ref_doc = dli.ref_doc_source AND dli.ref_doc_destination LIKE 'BLC%'";
            }
            $query = "SELECT $select_lib, sum(rd.montant) montant $select_nb
                FROM documents d
                $join_magasin
                $join_documents_liaisons
                LEFT JOIN reglements_docs rd ON d.ref_doc = rd.ref_doc AND rd.liaison_valide = 1
                LEFT JOIN reglements r ON rd.id_reglement = r.id_reglement
                LEFT JOIN reglements_types rt ON rt.id_reglement_type = r.id_reglement_type
                $join_devise
                WHERE $query_where AND rt.direction = ?
                GROUP BY $group_devise rt.id_reglement_type ORDER BY rt.id_reglement_type";

            return $this->db->run($query, $params);
        }

        $query = "SELECT d.ref_doc, rd.ref_reglement, rd.montant, r.date_reglement, rt.abrev rgm_mode, rt.direction as type_reglement
            FROM documents d
            $join_magasin
            LEFT JOIN reglements_docs rd ON d.ref_doc = rd.ref_doc AND rd.liaison_valide = 1
            LEFT JOIN reglements r ON rd.id_reglement = r.id_reglement
            LEFT JOIN reglements_types rt ON rt.id_reglement_type = r.id_reglement_type
            WHERE $query_where AND rt.direction = ?
            ORDER BY r.date_reglement";

        return $this->db->run($query, $params)->fetchAll(PDO::FETCH_GROUP | PDO::FETCH_OBJ);
    }

    public function getRGTFACFromPerdiodeAndReglement($search, $docs, $type = "entrant", $withDetails = false, $query_where = "", $link_table = 'doc_fac', $use_hours = false, $reglements = []) {
        $q_in_type_doc = implode(',', array_fill(0, count($docs[1]), '?'));
        if (isset($search['search_for']) &&  $search['search_for'] == 'reglement') {
            $query_where .= " d.id_etat_doc NOT IN (" . $q_in_type_doc . ") ";
            $params = $docs[1];
        } else {
            $query_where .= "d.id_type_doc = ? AND d.id_etat_doc NOT IN (" . $q_in_type_doc . ") ";
            $params = array_merge([$docs[0]], $docs[1]);
        }

        if (!empty($search['id_contact'])) {
            $query_where .= " AND d.id_contact = ? ";
            $params[] = $search['id_contact'];
        }

        if (!empty($search['id_caisse'])) {
            if ($search['id_caisse'] == 'NULL') {
                $query_where .= " AND d.id_caisse IS NULL ";
            } else {
                if (!is_array($search['id_caisse'])) {
                    $search['id_caisse'] = [$search['id_caisse']];
                }
                $q_in_caisse = implode(',', array_fill(0, count($search['id_caisse']), '?'));
                $query_where .= " AND d.id_caisse IN (" . $q_in_caisse . ") ";
                $params = array_merge($params, $search['id_caisse']);
            }
        }

        if (!empty($search['date_debut'])) {
            if ($use_hours) {
                $date_debut = lmbDateInter::DisplayToServerFormat($search['date_debut']);
            } else {
                $date_debut = lmbDateInter::ConvertServertoEntrepriseBeginOfDay($search['date_debut']);
            }

            if (isset($search['search_for']) && $search['search_for'] == 'reglement') {
                $query_where .=  " AND r.date_reglement >=  ?";
            } else {
                $query_where .=  " AND d.date_creation_doc >=  ?";
            }
            $params[] = $date_debut;
        }
        if (!empty($search['date_fin'])) {
            if ($use_hours) {
                $date_fin = lmbDateInter::DisplayToServerFormat($search['date_fin']);
            } else {
                $date_fin = lmbDateInter::ConvertServertoEntrepriseEndOfDay($search['date_fin']);
            }
            if (isset($search['search_for']) && $search['search_for'] == 'reglement') {
                $query_where .=  " AND r.date_reglement <= ?";
            } else {
                $query_where .= " AND d.date_creation_doc <= ?";
            }
            $params[] = $date_fin;
        }

        if (
            ((empty($datedebut)) && (empty($datefin)))
            ||
            (($search['date_debut'] == "----") && ($search['date_fin'] == "----"))
        ) {
            if (!empty($search['date_exercice'])) {
                $datesplit = explode(";", $search['date_exercice']);
                $query_where .= " AND d.date_creation_doc >= ? AND d.date_creation_doc <= ? ";
                $params[] = $datesplit[0] . " 00:00:00";
                $params[] = $datesplit[1] . " 23:59:59";
            }
        }

        $join_magasin = "";
        if (!empty($search['id_magasin'])) {
            if (!is_array($search['id_magasin'])) {
                $search['id_magasin'] = [$search['id_magasin']];
            }
            $q_in_magasin = implode(',', array_fill(0, count($search['id_magasin']), '?'));
            $join_magasin = "JOIN $link_table lt ON d.ref_doc=lt.ref_doc AND lt.id_magasin IN (" . $q_in_magasin . ")";
            $params = array_merge($search['id_magasin'], $params);
        }
        $sigle = $type == 'entrant' ? '>' : '<';
        $query_having_sup = "";
        if (!empty($rgts) && is_array($rgts)) {
            $q_in_doc = implode(',', array_fill(0, count(array_keys($rgts)), '?'));
            $query_having_sup = " OR d.ref_doc IN (" . $q_in_doc. ")";
            $params = array_merge($params, array_keys($rgts));
        }
        if (isset($search['search_for']) &&  $search['search_for'] == 'reglement') {
            $join_documents_liaisons = '';
        } else {
            $join_documents_liaisons = "LEFT JOIN documents_liaisons dli ON d.ref_doc = dli.ref_doc_source AND dli.ref_doc_destination LIKE 'BLC%'";
        }
        $query = "SELECT DISTINCT dle.ref_doc_line, dle.ref_doc, sum(dle.montant_ht) mt_ht, sum(dle.qte) nb_articles, sum(dle.montant_ttc) mt, ABS(sum(dle.montant_ttc)) ttc, ABS(sum(dle.montant_ttc_devise)) ttc_devise, d.id_devise, d.taux_conversion, a.nom_complet AS nom, d.date_creation_doc date, d.code_barre AS doc_barcode   
            FROM docs_lines_etendues dle
            INNER JOIN documents d ON dle.ref_doc = d.ref_doc
             $join_magasin
             $join_documents_liaisons
            LEFT JOIN contacts a ON a.id_contact = d.id_contact
            WHERE 1=1 AND $query_where
            GROUP BY d.ref_doc
            HAVING mt $sigle 0 $query_having_sup
            ORDER BY d.date_creation_doc";

        return $this->db->run($query, $params);
    }

    public function exportEditionsJournaux($infos) {
        $queryWhere = " WHERE 1 ";
        $params = [];
        $dateDebut = $dateFin = "";
        if (!empty($infos['dateDebut'])) {
            $dateDebut = $infos['dateDebut'];
            $queryWhere .= " AND d.date_creation_doc >= ? ";
            $params[] = $dateDebut;
        }

        if (!empty($infos['dateFin'])) {
            $dateFin = $infos['dateFin'];
            $queryWhere .= " AND d.date_creation_doc <= ? ";
            $params[] = $dateFin;
        }

        if (isset($infos['choixJournal'])) {
            $idJournal = $infos['choixJournal'];
            $queryWhere .= "AND cj.id_journal = ?";
            $params[] = $idJournal;
        }

        if (!empty($infos['recherche'])) {
            $recherche = $infos['recherche'];
            $queryWhere .= " AND (d.ref_doc LIKE ? OR ce.numero_compte LIKE ? OR pc.lib_compte LIKE ?)";
            $params[] = "%" . $recherche . "%";
            $params[] = "%" . $recherche . "%";
            $params[] = "%" . $recherche . "%";
        }

        if (isset($infos['exercice']) && ($dateDebut === "" && $dateFin === "")) {
            $datesExercices = explode(";", $infos['exercice']);
            $dateDebutExercice = $datesExercices[0];
            $dateFinExercice = $datesExercices[1];
            $queryWhere .= " AND d.date_creation_doc >= ? AND  d.date_creation_doc <= ? ";
            $params[] = $dateDebutExercice;
            $params[] = $dateFinExercice;
        }

        $query = "SELECT d.date_creation_doc , ce.numero_compte,
            pc.lib_compte, SUM(ce.montant) AS montant, d.ref_doc
            FROM compta_ecritures ce
            LEFT JOIN documents d ON d.ref_doc = ce.reference
            LEFT JOIN compta_mouvements cm ON ce.id_mouvement = ce.id
            LEFT JOIN plan_comptable pc ON pc.numero_compte = ce.numero_compte
            LEFT JOIN compta_journaux cj ON cj.id_journal = ce.id_journal
            " . $queryWhere . " AND type_compte IN ('G','X')
            GROUP BY d.ref_doc, ce.numero_compte
            ORDER BY d.date_creation_doc ASC";

        return $this->db->run($query, $params);
    }
    
    public function updateReglement_docsMontant($ref_doc, $id_reglement, $montant) {
        $query = "UPDATE reglements_docs SET montant = ? 
                    WHERE ref_doc = ? AND id_reglement = ?";
        return $this->db->run($query, [$ref_doc, $id_reglement, $montant]);
    }

    public function getValeurs_tvasForJs($ref_doc){
        $query = "SELECT IFNULL(dl.id_tva,0) id_tva,IFNULL(t.tva,0) taux,SUM(montant_ht_devise) base, SUM(montant_ttc_devise-montant_ht_devise) tva
                    FROM `docs_lines` dl
                    LEFT JOIN tvas t ON dl.id_tva=t.id_tva
                    WHERE `ref_doc` = ".$this->db->quote($ref_doc)."
                        AND dl.ref_article NOT IN ('INFO', 'INFORMATION', 'SSTOTAL')
                        AND dl.ref_article NOT LIKE 'TAXE%'
                    GROUP BY IFNULL(dl.id_tva,0)";
       $resultset = $this->db->query($query);
       return $resultset;
    }

    public function getRefs_docs_reception_marchandise($id_stock) {
        $statut = [\doc_cdf::ETAT_VALIDEE, \doc_cdf::ETAT_EN_COURS, \doc_blf::ETAT_EN_ATTENTE_RECEPTION, \doc_trm::ETAT_TRANSFERT_EN_COURS];
        $query = "SELECT d.ref_doc 
                FROM documents d 
                LEFT JOIN doc_cdf dc ON dc.ref_doc = d.ref_doc AND dc.id_stock IN ({$this->db->quoteList($id_stock)})
                LEFT JOIN doc_blf db ON db.ref_doc = d.ref_doc AND db.id_stock IN ({$this->db->quoteList($id_stock)})
                LEFT JOIN doc_trm dt ON dt.ref_doc = d.ref_doc AND dt.id_stock_cible IN ({$this->db->quoteList($id_stock)})
                WHERE d.id_type_doc IN ({$this->db->quoteList([\document::CDF, \document::BLF, \document::TRM])}) AND d.id_etat_doc IN ({$this->db->quoteList($statut)})
                ";

        return $this->db->run($query, []);
    }


    public function getQteTotaleByDocRef($ref, $refLivraison = null){
        if($refLivraison == null){
            $query = "SELECT ref_doc, sum(qte) as total FROM docs_lines  WHERE ref_doc= ? group by ref_doc";
            return $this->db->run($query,[$ref]);
        }
        else{
            $query = "SELECT ref_doc, sum(qte) as total FROM docs_lines  WHERE ref_doc= ? and ref_article != ? group by ref_doc";
            return $this->db->run($query,[$ref, $refLivraison]);
        }

    }

    public function getIdDocFromRefDoc($ref): \PDOStatement
    {
        $query = "SELECT id_doc FROM documents WHERE ref_doc = ?";
        return $this->db->run($query, [$ref]);
    }


    public function getIncidentsFaf($id_contact, $ignore_ref_doc = false) {
        $params = [$id_contact];
        $where = " ";
        if ($ignore_ref_doc) {
            $where = " AND d.ref_doc != ?";
            $params[] = $ignore_ref_doc;
        }
        $query = "SELECT d.ref_doc, d.id_etat_doc, d.date_creation_doc date_creation, d.id_devise, d.taux_conversion,
                SUM(dl.montant_ttc) as montant_ttc,
                ( SELECT SUM(montant)
                FROM reglements_docs rd
                LEFT JOIN reglements r ON r.id_reglement = rd.id_reglement
                WHERE d.ref_doc = rd.ref_doc AND r.valide = 1
            ) as montant_reglements, fim.lib
            FROM documents d
            LEFT JOIN docs_lines_etendues dl ON d.ref_doc = dl.ref_doc
            LEFT JOIN fournisseurs_incidents fi ON fi.source_id = d.id_doc
            LEFT JOIN fournisseurs_incidents_motifs fim ON fi.id_motif_incident = fim.id_motif_incident
            WHERE d.id_contact = ? AND fi.statut = 'open' {$where}
            GROUP BY d.ref_doc
            ORDER BY date_creation ASC";
        return $this->db->run($query, $params);
    }

    public function getListDocuments($payload, $pagination) {
        $queryWhere = " WHERE 1 = 1 ";
        $queryJoin = "";
        $params = [];

        if (!empty($payload['id_doc'])) {
            $queryWhere .= " AND d.id_doc = ?";
            $params[] = $payload['id_doc'];
        }

        if (!empty($payload['ref_doc'])) {
            $queryWhere .= " AND d.ref_doc = ?";
            $params[] = $payload['ref_doc'];
        }
        
        if (!empty($payload['code_barre'])) {
            $queryWhere .= " AND d.code_barre = ?";
            $params[] = $payload['code_barre'];
        }

        if (!empty($payload['inclure_type_doc'])) {
            $inclure_type_doc = "'" . implode ( "', '", explode(',', $payload['inclure_type_doc'])) . "'";
            $queryWhere .= " AND dt.code_doc IN ({$inclure_type_doc})";
        }

        if (!empty($payload['exclure_type_doc'])) {
            $exclure_type_doc = "'" . implode ( "', '", explode(',', $payload['exclure_type_doc'])) . "'";
            $queryWhere .= " AND dt.code_doc NOT IN ({$exclure_type_doc})";
        }

        if (!empty($payload['code_barre'])) {
            $queryWhere .= " AND d.code_barre = ?";
            $params[] = $payload['code_barre'];
        }

        if (!empty($payload['ref_doc_externe'])) {
            $queryWhere .= " AND d.ref_doc_externe = ?";
            $params[] = $payload['ref_doc_externe'];
        }

        if (!empty($payload['ref_doc_interne'])) {
            $queryWhere .= " AND d.ref_doc_interne = ?";
            $params[] = $payload['ref_doc_interne'];
        }

        if (!empty($payload['date_creation_min'])) {
            $queryWhere .= " AND d.date_creation_doc >= ?";
            $params[] = $payload['date_creation_min'];
        }

        if (!empty($payload['date_creation_max'])) {
            $queryWhere .= " AND d.date_creation_doc <= ?";
            $params[] = $payload['date_creation_max'];
        }

        if (!empty($payload['date_modification_min'])) {
            $queryWhere .= " AND d.date_modif >= ?";
            $params[] = $payload['date_modification_min'];
        }

        if (!empty($payload['date_modification_max'])) {
            $queryWhere .= " AND d.date_modif <= ?";
            $params[] = $payload['date_modification_max'];
        }

        if (!empty($payload['code_affaire'])) {
            $queryWhere .= " AND d.code_affaire = ?";
            $params[] = $payload['code_affaire'];
        }

        if (!empty($payload['id_magasin'])) {
            $queryJoin .= " LEFT JOIN doc_dev dev ON dev.ref_doc = d.ref_doc ";
            $queryJoin .= " LEFT JOIN doc_cdc cdc ON cdc.ref_doc = d.ref_doc ";
            $queryJoin .= " LEFT JOIN doc_blc blc ON blc.ref_doc = d.ref_doc ";
            $queryJoin .= " LEFT JOIN doc_fac fac ON fac.ref_doc = d.ref_doc ";
            $queryJoin .= " LEFT JOIN doc_def def ON def.ref_doc = d.ref_doc ";
            $queryJoin .= " LEFT JOIN doc_cdf cdf ON cdf.ref_doc = d.ref_doc ";
            $queryJoin .= " LEFT JOIN doc_blf blf ON blf.ref_doc = d.ref_doc ";
            $queryJoin .= " LEFT JOIN doc_faf faf ON faf.ref_doc = d.ref_doc ";

            $queryWhere .= " AND (blc.id_magasin = ? ||"
                    . " dev.id_magasin = ? ||"
                    . " cdc.id_magasin = ? ||"
                    . " fac.id_magasin = ? ||"
                    . " blf.id_magasin = ? ||"
                    . " cdf.id_magasin = ? ||"
                    . " def.id_magasin = ? ||"
                    . " faf.id_magasin = ?)";

            $params[] = $payload['id_magasin'];
            $params[] = $payload['id_magasin'];
            $params[] = $payload['id_magasin'];
            $params[] = $payload['id_magasin'];
            $params[] = $payload['id_magasin'];
            $params[] = $payload['id_magasin'];
            $params[] = $payload['id_magasin'];
            $params[] = $payload['id_magasin'];
        }

        if (!empty($payload['ref_doc_line'])) {
            $queryWhere .= " AND dle.ref_doc_line = ?";
            $params[] = $payload['ref_doc_line'];
        }

        if (!empty($payload['id_doc_line'])) {
            $queryWhere .= " AND dle.id_doc_line = ?";
            $params[] = $payload['id_doc_line'];
        }

        if (!empty($payload['ref_reglement'])) {
            $queryWhere .= " AND rd.ref_reglement = ?";
            $params[] = $payload['ref_reglement'];
        }

        if (!empty($payload['id_reglement'])) {
            $queryWhere .= " AND rd.id_reglement = ?";
            $params[] = $payload['id_reglement'];
        }
        
        $offset = ($pagination['page_to_show'] - 1) * $pagination['nb_par_pages'];

        $query = "
        SELECT d.ref_doc_interne, dle.ref_doc_line, dle.id_doc_line, d.id_doc, d.ref_doc, d.code_barre, d.description,
            d.date_creation_doc date_creation, d.ref_doc_externe, d.code_affaire,
            fi.statut, co.ref_crm, co.ref_contact, d.id_devise, d.taux_conversion, d.montant_ht, d.montant_ttc, dt.code_doc
            FROM  documents d
            JOIN documents_types dt ON d.id_type_doc = dt.id_type_doc
            LEFT JOIN reglements_docs rd ON d.ref_doc = rd.ref_doc AND rd.liaison_valide = 1
            LEFT JOIN fournisseurs_incidents fi ON fi.source_id = d.id_doc
            LEFT JOIN contacts co ON d.id_contact = co.id_contact
            LEFT JOIN docs_lines_etendues dle ON d.ref_doc = dle.ref_doc
            " . $queryJoin . $queryWhere . "
        GROUP BY d.ref_doc
        ORDER BY date_creation ASC";

        $total_count = $this->db->run("SELECT COUNT(*) FROM ({$query}) AS tmp", $params)->fetchColumn();

        $query .= " LIMIT {$offset}, {$pagination['nb_par_pages']}";
        $stmt = $this->db->run($query, $params);
        $result = $stmt->fetchAll(\PDO::FETCH_CLASS);
        
        return [
            'result' => $result,
            'pagination' => array(
                'page_to_show' => $pagination['page_to_show'],
                'nb_par_pages' => $pagination['nb_par_pages'],
                'total_count' => $total_count,
                'count' => count($result),
            ),
        ];
    }

    public function getFacturesAcquitteesNonPayeesIntegralement($infos,$page_to_show,$nb_par_page) {
      
        $query = "(SELECT docs.ref_doc
                    , dle_sum.montant_ht, dle_sum.montant_ttc, docs.date_creation_doc, COALESCE(rd.montant_reglements, 0) AS total_reglements, (dle_sum.montant_ttc - COALESCE(rd.montant_reglements, 0)) AS ecart
                    FROM documents docs
                     LEFT JOIN (
                      SELECT dle.ref_doc, SUM(dle.montant_ht) AS montant_ht, SUM(dle.montant_ttc) AS montant_ttc, SUM(dle.montant_tva) AS montant_tva
                      FROM docs_lines_etendues dle 
                      GROUP BY dle.ref_doc
                    ) dle_sum ON dle_sum.ref_doc = docs.ref_doc LEFT JOIN (
                    SELECT SUM(CASE WHEN rt.direction= 'entrant' THEN rd.montant WHEN rt.direction= 'sortant' THEN rd.montant * -1 END) as montant_reglements, rt.direction, ref_doc 
                    FROM reglements_docs rd 
                    LEFT JOIN reglements r ON r.id_reglement = rd.id_reglement 
                    LEFT JOIN reglements_types rt ON rt.id_reglement_type = r.id_reglement_type 
                    WHERE r.valide = 1 
                    GROUP BY rd.ref_doc
                ) rd ON docs.ref_doc = rd.ref_doc
                    WHERE docs.id_type_doc IN (4,15) AND docs.id_etat_doc NOT IN (16,17,59,60)";
    
        
        if (!empty($infos['date_debut'])) {
            $query .= " AND date_creation_doc >= '{$infos['date_debut']}'";
        }

        if(!empty($infos['date_fin'])){
            $query .= " AND date_creation_doc <= '{$infos['date_fin']}'";
        }
    
        
        if (!empty($infos['ecart'])) {
            $query .= " AND ABS(dle_sum.montant_ttc - COALESCE(rd.montant_reglements, 0)) > {$infos['ecart']}";
        } else {
            $query .= " AND ABS(dle_sum.montant_ttc - COALESCE(rd.montant_reglements, 0)) > 100";
        }
    
        $query .= " AND (rd.montant_reglements IS NULL OR ABS(rd.montant_reglements) < ABS(dle_sum.montant_ttc))
                    AND docs.id_etat_doc = '19'
                    GROUP BY docs.ref_doc 
                    ORDER BY date_creation_doc ASC, 1) 
                    UNION 
                    (SELECT docs.ref_doc
                    , dle_sum.montant_ht, dle_sum.montant_ttc, docs.date_creation_doc, COALESCE(rd.montant_reglements, 0) AS total_reglements, (dle_sum.montant_ttc - COALESCE(rd.montant_reglements, 0)) AS ecart
                    FROM documents docs
                     LEFT JOIN (
                      SELECT dle.ref_doc, SUM(dle.montant_ht) montant_ht, SUM(dle.montant_ttc) montant_ttc 
                      FROM docs_lines_etendues dle 
                      GROUP BY dle.ref_doc
                    ) dle_sum ON dle_sum.ref_doc = docs.ref_doc LEFT JOIN (
                    SELECT SUM(CASE WHEN rt.direction= 'sortant' THEN rd.montant WHEN rt.direction= 'entrant' THEN rd.montant * -1 END) as montant_reglements, rt.direction, ref_doc 
                    FROM reglements_docs rd 
                    LEFT JOIN reglements r ON r.id_reglement = rd.id_reglement 
                    LEFT JOIN reglements_types rt ON rt.id_reglement_type = r.id_reglement_type 
                    WHERE r.valide = 1 
                    GROUP BY rd.ref_doc
                ) rd ON docs.ref_doc = rd.ref_doc
                    WHERE docs.id_type_doc=8";
    

            if (!empty($infos['date_debut'])) {
                $query .= " AND date_creation_doc >= '{$infos['date_debut']}'";
            }

            if(!empty($infos['date_fin'])){
                $query .= " AND date_creation_doc <= '{$infos['date_fin']}'";
            }
    
        if (!empty($infos['ecart'])) {
            $query .= " AND ABS(dle_sum.montant_ttc - COALESCE(rd.montant_reglements, 0)) > {$infos['ecart']}";
        } else {
            $query .= " AND ABS(dle_sum.montant_ttc - COALESCE(rd.montant_reglements, 0)) > 100";
        }
    
        $query .= " AND (rd.montant_reglements IS NULL OR ABS(rd.montant_reglements) < ABS(dle_sum.montant_ttc))
                    AND docs.id_etat_doc = '35'
                    GROUP BY docs.ref_doc 
                    ORDER BY date_creation_doc ASC, 1)
                    ORDER BY date_creation_doc ASC, 1";
    
        return $this->db->paginate($query,[],$page_to_show,$nb_par_page,2);
    }
    
}
