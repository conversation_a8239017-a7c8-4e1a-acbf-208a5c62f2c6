<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

/**
 * Description of Article
 *
 * <AUTHOR>
 */

namespace LMBCore\WebServices\Controllers\Rovercash\Repository;
use LMB<PERSON>ore\DB\QueryAdapter\QueryAdapterFactory;


use LMBCore\Catalogue\PreparationZone;
use LMB<PERSON>ore\ESB2\Bundle\RoverCashAndroid\Sending\EntityProvider\ArticleProvider;
use LMBCore\ESB2\Model\Connecteur;

class Article {

    protected $_queryAdapter = null;
    
    protected function setQueryAdapter($queryAdapter){
        $this->_queryAdapter = $queryAdapter;
    }
    
    protected function getQueryAdapter(){
        if (is_null($this->_queryAdapter)){
            $this->setQueryAdapter(QueryAdapterFactory::getForClass(get_class($this)));
        }
        return $this->_queryAdapter;
    } 

    protected static function getStaticQueryAdapter(){
        return QueryAdapterFactory::getForClass(__CLASS__);
    }  


    public function get($params) {
        $response = array();

        if (empty($params['ref_erp'])) {
            return $response;
        }

        $response['ref_erp'] = $params['ref_erp'];

        if (empty($params['reappro'])) {
            return $response;
        }

        if ($params['reappro'] !== '1') {
            return $response;
        }

        $article = \article::getInstance($params['ref_erp']);

        $response['reappro'] = array("status" => "authorized", "data" => array());

        if (!\lmbconfig::getInstance()->get("HA_gestion_achats")) {
            $response['reappro']['status'] = "not_authorized";
        }

        // @ORDER BY date

        $resultat = $this->getQueryAdapter()->get($params);
        while ($var = $resultat->fetchObject()) {
            $cdf = ['qte' => $var->qte - $var->qte_recue];
            $cdf['date_livraison'] = empty($var->date_livraison) ? "" : $var->date_livraison;
            $cdf['id_stock'] = $var->id_stock;
            $response['reappro']['data'][] = $cdf;
        }

        return $response;
    }

    public function post($article, $identifiant_user) {

        $terminal = \caisses_terminaux::getTerminalByUserViaESB2($identifiant_user);
        $categ_cata = new \catalogue_categ($article['id_catalogue_categorie']);
        $id_catalogue = $categ_cata->getId_catalogue();
        $catalog = \catalogue::getTypeInstance($id_catalogue);

        $ref_art_categ = $categ_cata->getLiaison_ref_art_categ() ?? null;
        if (empty($ref_art_categ)) {
            if ($catalog->isLinkedToMainCatalog()) {
                return false;
            }

            if (in_array($terminal->getLogiciel(), [\caisses_terminaux::LOGICIEL_ROVERCASH, \caisses_terminaux::LOGICIEL_AIRKITCHEN])) {
                $lib = "Divers article " . ucfirst($terminal->getLogiciel());
                $ref_art_categ = \art_categ::getCategDefaut($terminal->getLogiciel(), $lib)->getRef_art_categ();
            }
        }

        if (empty($ref_art_categ)) {
            return false;
        }

        $infos_categ = \LMBCore\Article\Categorie::getInstanceByRef($ref_art_categ);

        $id_marque = false;
        if (!empty($article['id_marque'])) {
            $id_marque = $article['id_marque'];
        }
        $infos_generales = array();
        $infos_generales['ref_oem'] = '';
        $infos_generales['uuid_lm'] = $article['uuid_lm'];
        $infos_generales['ref_interne'] = (isset($article['reference1']) ? $article['reference1'] : '');
        $infos_generales['lib_article'] = (isset($article['lib']) ? $article['lib'] : '');
        $infos_generales['desc_courte'] = (isset($article['description']) ? $article['description'] : '');
        $infos_generales['desc_longue'] = (isset($article['recette']) ? $article['recette'] : '');
        $infos_generales['ref_art_categ'] = $ref_art_categ;
        $infos_generales['id_marque'] = $id_marque ? $id_marque : null;
        $infos_generales['id_fournisseur'] = null;
        $infos_generales['variante'] = 0;
        $infos_generales['date_debut_dispo'] = strftime(MYSQL_DATETIME_FORMAT);
        $cat_duree_months = $infos_categ->getDuree_dispo_months();
        $date_fin = mktime (date("H"),date("i"),date("s"), date("m")+$cat_duree_months, date("d"), date("Y"));
        $infos_generales['date_fin_dispo'] = strftime(MYSQL_DATETIME_FORMAT, $date_fin);
        $infos_generales['lot'] = 0;
        $infos_generales['gestion_sn'] = 0;
        if (isset($article['gestion_sn'])) {
            $infos_generales['gestion_sn'] = $article['gestion_sn'];
        }

        if(!empty($article['composition']) && is_array($article['composition']) && !empty($article['composition']['blocs']) && !empty($article['composition']['regles']) && !empty($article['id_article_composition'])){
            $infos_generales['lot'] = 2;
            $infos_generales['new_composition'] = true;
        } else if (!empty($article["nomenclatures"])) {
            $infos_generales['lot'] = \article::BDD_LOT_NOMENCLATURE;
        }

        if (isset($article['champs_externes'])) {
            $infos_generales['champs_externes'] = $article['champs_externes'];
        }

        if (!empty($article['code_barre1'])) {
            $infos_generales['code_barre'][] = $article['code_barre1'];
        }

        $infos_modele['poids_brut'] = 0;
        $infos_modele['poids_and_dim_options'][0]['poids'] = 0;

        if (isset($article['gestion_stock'])) {
            if (empty($article['gestion_stock'])) {
                $infos_generales['modele'] = 'service';
            } else {
                $infos_generales['modele'] = 'materiel';
            }
        } else {
            $infos_generales['modele'] = $infos_categ->getModele();
        }

        $infos_generales['prix_achat_ht'] = 0;

        if (!empty($article['ve_allow_retours'])) {
            $infos_generales['ve_allow_retours'] = $article['ve_allow_retours'];
        }
        if (!empty($article['ve_allow_remise_manuelle'])) {
            $infos_generales['ve_allow_remise_manuelle'] = $article['ve_allow_remise_manuelle'];
        }

        $id_tva = 0;
        $val_taxe = 0;

        // Si c'est le tarif principal du terminal, qu'il a un taux de TVA, et qu'on en a pas déjà, on le recup
        if(!isset($article['liste_taxes_vente']) && isset($article['liste_tarifs'][0]['liste_taxes_vente'])) {
            $article['liste_taxes_vente'] = $article['liste_tarifs'][0]['liste_taxes_vente'];
        }
        if (isset($article['liste_taxes_vente']) && !is_array($article['liste_taxes_vente'])) {
            $article['liste_taxes_vente'] = json_decode($article['liste_taxes_vente'], true);
        }

        if (!empty($article['liste_taxes_vente']) && is_array($article['liste_taxes_vente'])) {
            foreach ($article['liste_taxes_vente'] as $detail_taxe) {
                if (!empty($detail_taxe['ref_taxe'])) {
                    $pos = stripos($detail_taxe['ref_taxe'], 'TVA');
                    if ($pos !== false) {
                        $val_taxe = str_replace("TVA", "", $detail_taxe['ref_taxe']);
                        $id_tva = \tva::getId_tva_from_taux($val_taxe);
                    }
                }
            }
        }

        $id_tva_ha = 0;
        $val_taxe_ha = 0;
        if (!empty($article['liste_taxes_achat'])) {
            if (!is_array($article['liste_taxes_achat'])) {
                $article['liste_taxes_achat'] = json_decode($article['liste_taxes_achat'], true);
            }

            if (is_array($article['liste_taxes_achat'])) {
                foreach ($article['liste_taxes_achat'] as $detail_taxe) {
                    if (!empty($detail_taxe['ref_taxe'])) {
                        $pos = stripos($detail_taxe['ref_taxe'], 'TVA');
                        if ($pos !== false) {
                            $val_taxe_ha = str_replace("TVA", "", $detail_taxe['ref_taxe']);
                            $id_tva_ha = \tva::getId_tva_from_taux($val_taxe_ha);
                        }
                    }
                }
            }
        }

        if (!isset($article['pu_ttc'])) {
            if (!empty($article['liste_tarifs'])) {
                foreach($article['liste_tarifs'] as $rc_tarif) {
                    $id_tarif = $rc_tarif['id_tarif'];
                    if(empty($id_tarif)) {
                        continue;
                    }

                    if ($id_tarif == $terminal->getId_tarif()) {
                        $article['pu_ttc'] = $rc_tarif['pu_ttc'];
                        break;
                    }
                }
            }
            if (empty($article['pu_ttc'])){
                $article['pu_ttc'] = 0;
            }
        }

        // Génération des tarifs multiples sur AK
        $formules_tarifs = [];
        if($terminal->getLogiciel() == "airkitchen" && count($article['liste_tarifs']) > 1) {
            foreach ($article['liste_tarifs'] as $formule) {
                $formule_tarif = new \stdclass();

                $formule_tarif->lib = null;
                $formule_tarif->id_tarif = $formule['id_tarif'];
                $formule_tarif->indice_qte = 1;

                $formule['pu_ht'] = $formule['pu_ht']??0;
                $formule['pu_ttc'] = $formule['pu_ttc'] ?? 0;

                $formule_tarif->pu_ht = $formule['pu_ht'];
                $formule_tarif->pu_ttc = $formule['pu_ttc'];
                $formule_tarif->formule_tarif = "PU_TTC=".$formule['pu_ttc'];

                $formules_tarifs[] = $formule_tarif;
            }
        }

        $article['pu_ht'] = ttc2ht($article['pu_ttc'], $val_taxe);
        $infos_generales['id_tva'] = convert_numeric($id_tva);
        $infos_generales['prix_public_ht'] = $article['pu_ht'];

        $infos_generales['id_tva_ha'] = convert_numeric($id_tva_ha);
        if (isset($article['pa_fournisseur_ht'])) {
            $infos_generales['prix_achat_ht'] = $article['pa_fournisseur_ht'];
        } elseif (isset($article['pa_fournisseur_ttc'])) {
            $infos_generales['prix_achat_ht'] = ttc2ht($article['pa_fournisseur_ttc'], $val_taxe_ha);
        }

        if (isset($article['ordre_preparation'])) {
            $infos_generales['ordre_preparation'] = $article['ordre_preparation'];
        }

        if (isset($article['qte_nb_decimales'])) {
            $infos_generales['qte_nb_decimales'] = $article['qte_nb_decimales'];
        }
        if (isset($article['price_nb_decimales'])) {
            $infos_generales['price_nb_decimales'] = $article['price_nb_decimales'];
        }
        if (isset($article['valeur_achat_nb_decimales'])) {
            $infos_generales['valeur_achat_nb_decimales'] = $article['valeur_achat_nb_decimales'];
        }


        $id_fournisseur_favori = null;
        if (!empty($article['id_fournisseur'])) {
            $id_fournisseur_favori = $article['id_fournisseur'];
        }
        $infos_generales['id_fournisseur_favori'] = $id_fournisseur_favori ? $id_fournisseur_favori : null;

        $caracs = array();
        if (!empty($article['caracs']) && is_array($article['caracs'])) {
            foreach ($article['caracs'] as $caracteristique) {
                if (empty($caracteristique['valeur'])) {
                    continue;
                }
                $id_carac = $caracteristique['id_carac'];
                //normalement, pas possible d'avoir une carac pas existante
                if (empty($id_carac)) {
                    throw new \ApiRestException("La caractéristique n'existe pas", 500, \ApiRestException::ERR_INVALID_DATA_FORMAT);
                }
                $carac = new \stdclass;
                $carac->id_carac = $id_carac;
                $carac->valeur = $caracteristique['valeur'];
                $caracs[] = $carac;
            }
        }

        if (!empty($article['ordre_preparation'])) {
            $infos_generales['ordre_preparation'] = $article['ordre_preparation'];
        }

        $article_obj = \article::create($infos_generales, $infos_modele, $caracs, $formules_tarifs);

        if (!$article_obj->getRef_article()) {
            return false;
        }

        if(!$catalog->isLinkedToMainCatalog()){
            $this->addArticleToCatalogCateg($article_obj->getRef_article(), $article['id_catalogue_categorie']);
        }

        if (!empty($article['composition'])) {
            $article['composition'] = self::handleComposition($article['composition'], $article_obj);
        } else if (!empty($article["nomenclatures"])) {
            $regles = $article["nomenclatures"]["regles"] ?? [];
            foreach ($regles as &$regle) {
                if (!empty($regle["ref_article"])) {
                    //TODO: check uuid_lm
                    // $regle["ref_article"] = $this->canal->getRef_lmb($regle["ref_article"], [LIAISON_TYPE_ARTICLE_VARIANTE, LIAISON_TYPE_ARTICLE]);
                    // $regle["ref_article"] = $this->canal->getRef_lmb($regle["ref_article"], [LIAISON_TYPE_ARTICLE_VARIANTE, LIAISON_TYPE_ARTICLE]);

                }
                if (!empty($regle["liste_articles"])) {
                    $regle["liste_articles"] = array_map(function($ref) {
                        //TODO: check uuid_lm
                        // return $this->canal->getRef_lmb($ref, [LIAISON_TYPE_ARTICLE_VARIANTE, LIAISON_TYPE_ARTICLE]);
                        return $ref;
                    }, $regle["liste_articles"]);
                }
                if (!empty($regle["liste_categs"])) {
                    $regle["liste_categs"] = array_map(function($ref) {
                        //TODO: check uuid_lm
                        return $ref;
                        // return \catalogue::getRefArtCategFromCategId($this->canal->getRef_lmb($ref, LIAISON_TYPE_ART_CATEG));
                    }, $regle["liste_categs"]);
                }
            }
            $article_obj->setNomenclatures($article["nomenclatures"]["sections"] ?? [], $regles);
        }
        $article_obj->add_formule_tarif($terminal->getId_tarif(), 1, "PU_TTC=" . trim(str_replace(",", ".", $article['pu_ttc'])));
        if (isset($article['statut_declinaison']) && isset($article['id_declinaison_groupe'])) {
            $article['statut_declinaison'] = empty($article['statut_declinaison']) ? null : $article['statut_declinaison'];
            $article['id_declinaison_groupe'] = empty($article['id_declinaison_groupe']) ? null : $article['id_declinaison_groupe'];
            $article['id_declinaison_groupe'] = empty($article['statut_declinaison']) ? null : $article['id_declinaison_groupe'];

            if (!empty($article['id_declinaison_groupe'])) {
                $article['id_declinaison_groupe'] = $article['id_declinaison_groupe'];
            }
            if (!empty($article['id_declinaison_groupe'])) {
                $article_obj->setStatut_groupe($article['statut_declinaison']);
                $article_obj->setId_article_groupe($article['id_declinaison_groupe']);
            }
        }
        if (!empty($article['stock1'])) {
            if ($article_obj->isStockable() && \lmbconfig::getInstance()->get("STK_use_stocks")) {
                $article_obj->ajuster_stock($article['stock1'], $terminal->getId_stock());
            }
        }
        if (!empty($article['id_preparation_zone']) && !is_array($article['id_preparation_zone'])) {
            throw new \ApiRestException("id_preparation_zone doit etre un tableau", 500, \ApiRestException::ERR_INVALID_DATA_FORMAT);
        }

        if (!empty($article['id_preparation_zone']) && $article['id_preparation_zone'] != -1 && $article['id_preparation_zone'] != "[-1]") {
            PreparationZone::setForArticle($article_obj->getId_article(), (array) $article['id_preparation_zone']);
        }
        $article_rc = (new ArticleProvider())->createArticleRC($article_obj, $terminal);
        return $article_rc->toArray();
    }
    
    private static function handleComposition($composition, $article) {
        $infos_composition = array();

        $infos_composition['id_article_compose'] = $article->getId_article();

        $infos_composition['regles'] = array();
        if (!empty($composition['regles'])) {
            foreach ($composition['regles'] as $regle) {

                $param_regle = $regle['paramJson'];
                if (!is_array($param_regle)) {
                    $param_regle = json_decode($param_regle, true);
                }

                if ($regle['ref_regle'] == "articleIn") {
                    if (!empty($param_regle['articles'])) {
                        $articles_convertis = array();
                        foreach ($param_regle['articles'] as $id_article_regle) {
                            $ref_article_regle = $id_article_regle;

                            if (empty($ref_article_regle))
                                throw new \ApiRestException($id_article_regle . " règle de compostion de " . $article->getRef_article() . " pas encore lié", 500, \ApiRestException::ERR_INVALID_DATA_FORMAT);

                            $article_regle = \article::getInstance($ref_article_regle);
                            if (empty($article_regle))
                                throw new \ApiRestException($id_article_regle . " règle de compostion de " . $article->getRef_article() . " pas encore lié", 500, \ApiRestException::ERR_INVALID_DATA_FORMAT);

                            $articles_convertis[] = $article_regle->getId_article();
                        }

                        $param_regle['articles'] = $articles_convertis;
                    }
                }

                $regle_param = json_encode($param_regle);
                $infos_composition['regles'][] = array('ref_regle' => $regle['ref_regle'], 'paramJson' => $regle_param);
            }
        }

        $infos_composition['composants'] = array();
        if (!empty($composition['composants'])) {
            foreach ($composition['composants'] as $composant) {
                $id_composant = $composant['id_article'];

                if (empty($id_composant))
                    throw new \ApiRestException($id_composant . " composant de " . $article->getRef_article() . " pas encore lié", 500, \ApiRestException::ERR_INVALID_DATA_FORMAT);

                $art_composant = \article::getInstance($id_composant);
                if (empty($art_composant))
                    throw new \ApiRestException($id_composant . " composant de " . $article->getRef_article() . " pas encore lié", 500, \ApiRestException::ERR_INVALID_DATA_FORMAT);

                $infos_composition['composants'][] = array('id_article' => $art_composant->getId_article(), 'qte' => !empty($composant['qte']) ? $composant['qte'] : 1);
            }
        }

        $infos_composition['blocs'] = !empty($composition['blocs']) ? $composition['blocs'] : array();

        if ($article->getId_article_composition()) {
            $composition = \LMBCore\Article\Composition::getInstanceByIdArticle($article->getId_article());
            $composition->updateFromInfos($infos_composition);
        } else {
            \LMBCore\Article\Composition::createFromInfos($infos_composition);
        }

        return $composition;
    }

    public function addArticleToCatalogCateg($ref_article, $id_catalogue_categ){
        $already_exist = \catalogue_categ_content::getContentByCategorieAndArticle($id_catalogue_categ, $ref_article);
//        if(!empty($already_exist) && $already_exist->getId_catalogue_categ() != $id_catalogue_categ)
//            $already_exist->delete();

        if(empty($already_exist)){
            $cont = new \catalogue_categ_content();
            $cont->addDatas(["ref_article" => $ref_article]);
            $cont->setId_catalogue_categ($id_catalogue_categ);
            $cont->save();
        }
    }

}
