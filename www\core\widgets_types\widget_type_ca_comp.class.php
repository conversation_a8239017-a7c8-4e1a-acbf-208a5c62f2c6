<?php

class widget_type_ca_comp extends widget_type_abstract{

    const AVAILABLE_VARIABLES_FIELDS = array('id_magasin_enseigne', 'id_magasin', 'period');
    const AVAILABLE_PARAMS = array('datas_type' => 'ca');
    protected $defaut_cache_ttl = 43200;

    public function getDescription() {
        return __(182310,"Affiche le chiffre d’affaires HT (ou volume) sur la période choisie et compare avec la même période de l’année précédente");
    }

    public function getDescriptionTechnique() {
        return __(182311,"Est basé sur le chiffre d’affaires ou le volume d’affaires")."<br/>
            <ul><li>".__(182313,'Chiffre d’affaires : Somme des factures en statut "à régler" + "acquittées"')."</li>
            <li>".__(182314,'Volume d’affaires : Somme des factures en statut "à régler" + "acquittées" + Somme des commandes tous statuts non reliées aux factures en statut "à régler" ou "acquittées"')."</li>
            <li>".__(182315,"Chiffre d’affaires à emporter : somme des tickets de caisse ou facture associée (soit l’un soit l’autre) issus de la vente à emporter AirKitchen")."</li>
            <li>".__(182316,"Chiffre d’affaires sur place : somme des tickets de caisse (ou facture) issus de la vente sur place / à table AirKitchen")."</li></ul>";
    }

    public static function getThisParamsLibs() {
        return [
            'datas_type' => [
                'description' => __(182425,"Chiffre d’affaires, volume d’affaires, chiffre d’affaires sur place ou à emporter (uniquement si terminal AirKitchen synchronisé pour ces deux derniers)")
            ]
        ];
    }

    // @TODO - Utiliser ça pour gérer l'affichage des champs dans la popup coté admin
    public static function getThisParamsValues() {
        return [
            "datas_type" => [
                "ca" => __(410290,"Chiffre d'affaires"),
                "va" => __(510067,"Volume d'affaires"),
                "ca_sur_place" => __(410186,"Chiffre d'affaires sur place"),
                "ca_a_emporter" => __(410187,"Chiffre d'affaires emporté")
            ]
        ];
    }

    public function getPreviewDatas($params_supp = array()) {
        return array(
            'montant' => "924 000",
            'evo' => (float)-1.93
        );
    }

    public function getDatas($params_supp = array()){
        $bdd = PDO_etendu::getInstance();
        $offsetServer = \lmbDateInter::getServerTZOffset(true);
        $offsetEntreprise = \lmbDateInter::getEntrepriseTZOffset(true);
        $where = "";
        $where_doc = "(d.id_type_doc = 2 AND d.id_etat_doc <> 7) OR (d.id_type_doc = 4 AND d.id_etat_doc IN(18,19))";

        $datas_type = $this->getWidget()->getParam("datas_type", 'ca');

        if(!empty($params_supp['id_magasin_enseigne']))
            $where .= " AND m.id_magasin_enseigne IN(".$bdd->quoteList($params_supp['id_magasin_enseigne']).")";

        if(!empty($params_supp['id_magasin']))
            $where .= " AND m.id_magasin IN(".$bdd->quoteList($params_supp['id_magasin']).")";

        $last_date_debut = "";
        $last_date_fin = "";

        $period = $params_supp['period'] ?? [];

        $return = array(
            'type_period' => !empty($period) ? 'perso' : 'year'
        );

        if(empty($period['date_debut'])){
            $period['date_debut'] = date('Y-01-01 00:00:00');
            $last_date_debut = $bdd->quote(date('Y-01-01 00:00:00', strtotime("-1 year")));
        }
        if(empty($period['date_fin'])){
            $period['date_fin'] = date('Y-12-31 23:59:59');
            $last_date_fin = $bdd->quote(date('Y-12-31 23:59:59', strtotime("-1 year")));
        }

        $date_debut = $bdd->quote($period['date_debut']);
        $date_fin = $bdd->quote($period['date_fin']);

        if(empty($last_date_debut)) {
            $last_date_debut = $this->getDateYearBefore($period['date_debut']);
            $last_date_debut = $bdd->quote($last_date_debut);
        }
        if(empty($last_date_fin)) {
            $last_date_fin = $this->getDateYearBefore($period['date_fin']);
            $last_date_fin = $bdd->quote($last_date_fin);
        }

        $condition = "1=1";
        $join = "";
        $montant = "dl.montant_ht";
        switch($datas_type){
            case "ca":
                $join = " 
                    LEFT JOIN documents_liaisons dlink ON d.ref_doc=dlink.ref_doc_destination AND dlink.ref_doc_source LIKE 'TIC-%' 
                    LEFT JOIN documents_liaisons dlink2 ON d.ref_doc=dlink2.ref_doc_source AND dlink2.ref_doc_destination LIKE 'TIC-%'
                ";
                $where_doc = " (d.id_type_doc = 15 AND d.id_etat_doc = 62) OR (d.id_type_doc = 4 AND d.id_etat_doc IN (18,19) AND dlink.ref_doc_source IS NULL AND dlink2.ref_doc_destination IS NULL) ";
                break;
            case "va":
                $join = "LEFT JOIN documents_liaisons blc_fac ON blc_fac.ref_doc_destination = fac.ref_doc LEFT JOIN documents_liaisons cdc_blc ON cdc_blc.ref_doc_destination = blc_fac.ref_doc_source";
                $condition = "( d.id_type_doc = 2 OR ( d.id_type_doc = 4 AND cdc_blc.ref_doc_source IS NULL ) )";
                break;
            case "ca_sur_place":
                if(self::is_ak_available()){
                    $join = "JOIN docs_caracs dc ON dc.ref_doc = d.ref_doc JOIN caracs ca ON ca.id_carac = dc.id_carac AND dc.value IN ('surPlace', 'venteSurPlace') AND ca.ref_carac = 'ak_type_commande'";
                    $where_doc = "(d.id_type_doc = 15 AND d.id_etat_doc = 62) OR (d.id_type_doc = 4 AND d.id_etat_doc IN (18,19) AND d.ref_doc NOT IN (SELECT ref_doc_destination FROM `documents_liaisons` WHERE `ref_doc_source` LIKE 'TIC-%'))";
                }
                break;
            case "ca_a_emporter":
                if(self::is_ak_available()){
                    $join = "JOIN docs_caracs dc ON dc.ref_doc = d.ref_doc JOIN caracs ca ON ca.id_carac = dc.id_carac AND dc.value = 'aEmporter' AND ca.ref_carac = 'ak_type_commande'";
                    $where_doc = "(d.id_type_doc = 15 AND d.id_etat_doc = 62) OR (d.id_type_doc = 4 AND d.id_etat_doc IN (18,19) AND d.ref_doc NOT IN (SELECT ref_doc_destination FROM `documents_liaisons` WHERE `ref_doc_source` LIKE 'TIC-%'))";
                }
                break;
        }

        $calculs = array(
            "ROUND(SUM(IF(CONVERT_TZ(d.date_creation_doc,'$offsetServer','$offsetEntreprise') >= {$date_debut} AND CONVERT_TZ(d.date_creation_doc,'$offsetServer','$offsetEntreprise') <= {$date_fin} AND {$condition}, {$montant}, 0)), 2) montant",
            "ROUND(SUM(IF(CONVERT_TZ(d.date_creation_doc,'$offsetServer','$offsetEntreprise') >= {$last_date_debut} AND CONVERT_TZ(d.date_creation_doc,'$offsetServer','$offsetEntreprise') <= {$last_date_fin} AND {$condition}, {$montant}, 0)), 2) last_montant"
        );

        $select_calculs = implode(", ", $calculs);

        $query = "
            SELECT {$select_calculs}
            FROM documents d
            LEFT JOIN doc_cdc cdc ON cdc.ref_doc = d.ref_doc
            LEFT JOIN doc_fac fac ON fac.ref_doc = d.ref_doc
            LEFT JOIN doc_tic tic ON tic.ref_doc = d.ref_doc
            LEFT JOIN magasins m ON m.id_magasin = fac.id_magasin OR m.id_magasin = cdc.id_magasin OR m.id_magasin = tic.id_magasin
            JOIN docs_lines dl ON dl.ref_doc=d.ref_doc
            {$join}
            WHERE dl.is_chiffre_affaires = 1 AND dl.id_doc_line_parent is NULL AND dl.visible = 1 AND ({$where_doc}) 
            {$where}
        ";

        $results = $bdd->query($query)->fetchObject();

        $value = intval($results->montant);
        $last = intval($results->last_montant);
        if($last == 0) {
            $evo_montant = "N/D";
        } else {
            $evo_montant = ($last != 0 ? (($value - $last) / $last) * 100 : 100);
        }

        $over_100000 = $value >= 100000;
        $return['montant'] = number_format($over_100000  ? $results->montant / 1000 : $results->montant, 2, ',', ' ') . ($over_100000 ? "K" : "");
        $return['evo'] = $evo_montant;

        return (object)$return;
    }

    public function getVersion(){
        return 1;
    }

    public static function is_ak_available(){
        $terminaux_ak = caisses_terminaux::getList("logiciel = 'airkitchen' AND date_archivage IS NULL");
        return count($terminaux_ak) > 0;
    }

}

