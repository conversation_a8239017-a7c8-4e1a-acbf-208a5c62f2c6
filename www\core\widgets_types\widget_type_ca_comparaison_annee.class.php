<?php

class widget_type_ca_comparaison_annee extends widget_type_abstract
{
    const AVAILABLE_VARIABLES_FIELDS = array('id_magasin', 'id_magasin_enseigne');

    public function getDescription() {
        return __(182442,"Affiche sur un diagramme en barres le chiffre d’affaires de l’année en cours et le compare avec la période N-1")."<br/>".
            __(182443,"Période N-1: correspond à la même période que l’année en cours, pour l’année antérieure").
            "<ul><li>".__(182444,"Exemple : si l’année en cours (borne limite = jour actuel) est du 01/01/21 au 07/12/21 alors la période N-1 correspondante sera du 01/01/20 au 07/12/20")."</li></ul>";
    }

    public function getDescriptionTechnique()
    {
        return __(182427,'Est basé sur les ventes : Somme des factures en statut "à régler" + "acquittées"');
    }

    public function getPreviewDatas()
    {
        $current_year = (int)date("Y");
        return [
            ['montant_ht' => "21000", 'annee' => $current_year, 'periode' => 12],
            ['montant_ht' => "5000", 'annee' => $current_year, 'periode' => 11],
            ['montant_ht' => "10000", 'annee' => $current_year, 'periode' => 10],
            ['montant_ht' => "2000", 'annee' => $current_year, 'periode' => 9],
            ['montant_ht' => "25000", 'annee' => $current_year, 'periode' => 8],
            ['montant_ht' => "7000", 'annee' => $current_year, 'periode' => 7],
            ['montant_ht' => "9000", 'annee' => $current_year, 'periode' => 6],
            ['montant_ht' => "3000", 'annee' => $current_year, 'periode' => 5],
            ['montant_ht' => "15000", 'annee' => $current_year, 'periode' => 4],
            ['montant_ht' => "10000", 'annee' => $current_year, 'periode' => 3],
            ['montant_ht' => "5000", 'annee' => $current_year, 'periode' => 2],
            ['montant_ht' => "1000", 'annee' => $current_year, 'periode' => 1],
            ['montant_ht' => "13000", 'annee' => $current_year - 1, 'periode' => 12],
            ['montant_ht' => "12000", 'annee' => $current_year - 1, 'periode' => 11],
            ['montant_ht' => "8000", 'annee' => $current_year - 1, 'periode' => 10],
            ['montant_ht' => "7000", 'annee' => $current_year - 1, 'periode' => 9],
            ['montant_ht' => "4000", 'annee' => $current_year - 1, 'periode' => 8],
            ['montant_ht' => "8000", 'annee' => $current_year - 1, 'periode' => 7],
            ['montant_ht' => "15000", 'annee' => $current_year - 1, 'periode' => 6],
            ['montant_ht' => "1000", 'annee' => $current_year - 1, 'periode' => 5],
            ['montant_ht' => "35000", 'annee' => $current_year - 1, 'periode' => 4],
            ['montant_ht' => "9000", 'annee' => $current_year - 1, 'periode' => 3],
            ['montant_ht' => "5000", 'annee' => $current_year - 1, 'periode' => 2],
            ['montant_ht' => "7000", 'annee' => $current_year - 1, 'periode' => 1]
        ];
    }

    public function getDatas($params_supp = array())
    {
        $bdd = self::getDataBddInstance();
        $offsetServer = \lmbDateInter::getServerTZOffset(true);
        $offsetEntreprise = \lmbDateInter::getEntrepriseTZOffset(true);
        $fields = $this->getDefaultFieldsValues($params_supp);

        $queryMagasin = "";
        if (!empty($fields['id_magasin'])) {
            $queryMagasin .= " && m.id_magasin IN (".implode(',',$fields['id_magasin']).")";
        }
        $queryEnseigne = "";
        if (!empty($fields['id_magasin_enseigne'])) {
            $queryEnseigne = " && m.id_magasin_enseigne IN (".implode(',', $fields['id_magasin_enseigne']).")";
        }

        $query = "SELECT ROUND(SUM(dl.montant_ht)) as montant_ht, YEAR(CONVERT_TZ(d.date_creation_doc,'$offsetServer','$offsetEntreprise')) AS annee, MONTH(CONVERT_TZ(d.date_creation_doc,'$offsetServer','$offsetEntreprise')) AS periode
                    FROM documents d
                    JOIN documents_types dt ON d.id_type_doc = dt.id_type_doc
                    JOIN documents_etats de ON d.id_etat_doc = de.id_etat_doc
                    JOIN doc_fac df ON df.ref_doc = d.ref_doc
                    JOIN docs_lines dl ON dl.ref_doc=d.ref_doc
                    LEFT JOIN magasins m ON m.id_magasin=df.id_magasin
                    WHERE d.id_etat_doc IN (18,19) AND dl.is_chiffre_affaires = 1
                    && CONVERT_TZ(d.date_creation_doc,'$offsetServer','$offsetEntreprise') >= DATE_ADD(CURDATE(),INTERVAL -2 YEAR)
                    {$queryMagasin} {$queryEnseigne}
                    GROUP BY YEAR(CONVERT_TZ(d.date_creation_doc,'$offsetServer','$offsetEntreprise')), MONTH(CONVERT_TZ(d.date_creation_doc,'$offsetServer','$offsetEntreprise'))
                    ORDER BY df.id_magasin, CONVERT_TZ(d.date_creation_doc,'$offsetServer','$offsetEntreprise') DESC;";

        return $bdd->query($query)->fetchAll(PDO::FETCH_ASSOC);
    }

    public function getVersion()
    {
        return 1;
    }

    public function getDynamicParams($params_supp = null)
    {
        $serie = new statistiques_widget_datas_serie(statistiques_widget_datas_serie::DEFAULT_NAME, null, 1800);
        $series[] = $serie;
        return $series;
    }
}