<?php

class widget_type_ca_progress_y extends widget_type_abstract{

    const AVAILABLE_VARIABLES_FIELDS = array('id_magasin_enseigne', 'id_magasin');
    const AVAILABLE_PARAMS = array('objectif' => 'year', 'datas_type' => 'ca', 'target_ca' => null);

    protected $defaut_cache_ttl = 86400;

    public function getDescription() {
        return __(182421,"Affiche l’objectif mensuel ou annuel du chiffre d’affaires (ou volume) à atteindre ainsi que le chiffre d’affaires actuel (ou volume) et le pourcentage d’atteinte de l’objectif associé");
    }

    public function getDescriptionTechnique()
    {
        return "<ul><li>".__(182422,"Est basé sur le chiffre d’affaires ou le volume d’affaires")."</li>
            <li>".__(182423,'Chiffre d’affaires : Somme des factures en statut "à régler" + "acquittées"')."</li>
            <li>".__(182424,'Volume d’affaires : Somme des factures en statut "à régler" + "acquittées" + Somme des commandes tous statuts non reliées aux factures en statut "à régler" ou "acquittées"')."</li></ul>";
    }

    public static function getThisParamsLibs() {
        return [
            'datas_type' => [
                'description' => __(182420,"Chiffre d’affaires ou volume d’affaires")
            ]
        ];
    }

    public static function getThisParamsValues() {
        return [
            "datas_type" => [
                "ca" => __(410290,"Chiffre d'affaires"),
                "va" => __(510067,"Volume d'affaires")
            ],
            "objectif" => [
                "year" => __(182551,"Annuel"),
                "month" => __(182552,"Mensuel")
            ]
        ];
    }

    public function getPreviewDatas(){
        return array(
            "montant" => number_format(30, 2, ',', ' ') . "K",
            "target" => number_format(57, 2, ',', ' ') . "K",
            "evo" => 56
        );
    }

    public function getDatas($params_supp = array()){
        $bdd = PDO_etendu::getInstance();
        $where = "";
        $offsetServer = \lmbDateInter::getServerTZOffset(true);
        $offsetEntreprise = \lmbDateInter::getEntrepriseTZOffset(true);
        $objectif = $this->getWidget()->getParam("objectif", "year");
        $datas_type = $this->getWidget()->getParam("datas_type", "ca");

        if(!empty($params_supp['id_magasin_enseigne']))
            $where .= " AND m.id_magasin_enseigne IN(".$bdd->quoteList($params_supp['id_magasin_enseigne']).")";

        if(!empty($params_supp['id_magasin']))
            $where .= " AND m.id_magasin IN(".$bdd->quoteList($params_supp['id_magasin']).")";
        if ($datas_type == "ca") {
            $condition =  "d.id_type_doc = 4";
            $queryJoin = "";
        } else {
            $condition = "d.id_type_doc = 2 OR ( d.id_type_doc = 4 AND cdc_blc.ref_doc_source IS NULL )";
            $queryJoin = "
                LEFT JOIN documents_liaisons blc_fac ON blc_fac.ref_doc_destination = fac.ref_doc
                LEFT JOIN documents_liaisons cdc_blc ON cdc_blc.ref_doc_destination = blc_fac.ref_doc_source
            ";
        }
        $date = $objectif == "year" ? "DATE_FORMAT(NOW(), '%Y-01-01')" : "DATE_FORMAT(NOW(), '%Y-%m-01')";

        $query = "
            SELECT ROUND(SUM(IF({$condition}, dl.montant_ht, 0)), 2) montant
            FROM documents d
            JOIN docs_lines dl ON dl.ref_doc = d.ref_doc
            LEFT JOIN doc_cdc cdc ON cdc.ref_doc = d.ref_doc
            LEFT JOIN doc_fac fac ON fac.ref_doc = d.ref_doc
            LEFT JOIN magasins m ON m.id_magasin = fac.id_magasin OR m.id_magasin = cdc.id_magasin
            {$queryJoin}
            WHERE dl.id_doc_line_parent IS NULL AND dl.visible = 1 AND dl.is_chiffre_affaires = 1 AND ( (d.id_type_doc = 2 AND d.id_etat_doc <> 7) OR (d.id_type_doc = 4 AND d.id_etat_doc IN(18,19)) ) AND CONVERT_TZ(d.date_creation_doc,'$offsetServer','$offsetEntreprise') >= {$date}
            {$where}
        ";

        $res = $bdd->query($query)->fetchColumn();

        $obj = 0;

        $target = $this->getWidget()->getParam('target_ca') ?? $this->getWidget()->getWidgetType()->getDefaultParams('data', 'target_ca') ?? [];
        if(!empty($target)){
            $id_magasin = [];
            // On calcul l'objectif en faisant la somme des objectifs de chaque magasins actuellement sélectionné
            if(!empty($params_supp['id_magasin'])) {
                $id_magasin = array_merge($id_magasin, $params_supp['id_magasin']);
            }

            $enseigne_magasin_ids = [];
            if(!empty($params_supp['id_magasin_enseigne']) && is_array($params_supp['id_magasin_enseigne'])) {
                $enseigne_magasin_ids = $this->getMagasinIdsFromEnseigneIds($params_supp['id_magasin_enseigne']);
            }
            if(empty($id_magasin)) $id_magasin = $enseigne_magasin_ids;
            if(!empty($enseigne_magasin_ids)) $id_magasin = array_intersect($id_magasin, $enseigne_magasin_ids);
            
            if(!empty($id_magasin)){
                $obj = 0;
                foreach($id_magasin as $id) {
                    $obj += (float) ($target['center_'.$id] ?? 0);
                }
            } elseif(empty($id_magasin) && empty($enseigne_magasin_ids)) {
                foreach ($target as $key => $value) {
                    if ($value == '') $target[$key] = 0;
                }
                $obj = array_sum($target);
            }
        }

        $evo = 0;
        if ($obj > 0) $evo = round($res * 100 / $obj);
        else if ($res > 0) $evo = 100;

        $ca_over_100000 = $res >= 100000;
        $target_over_100000 = $obj >= 100000;

        $progress = array(
            "montant" => statistiques_widget::roundPriceValue($res),
            "target" => statistiques_widget::roundPriceValue($obj),
            "evo" => $evo
        );

        return $progress;
    }

    public function getVersion(){
        return 1;
    }

    /**
     * @param array $magasinEnseigneIds
     * @return array
     */ 
    private function getMagasinIdsFromEnseigneIds($magasinEnseigneIds) {
        $bdd = PDO_etendu::getInstance();
        $magasin_enseigne_ids = implode(',', $magasinEnseigneIds);
        $query = "
            SELECT id_magasin
            FROM magasins
            WHERE id_magasin_enseigne IN ($magasin_enseigne_ids)
        ";
        $result = $bdd->query($query);
        while ($mgs = $result->fetchObject()) {
            $enseigne_magasin_ids[] = $mgs->id_magasin;
        }
        return $enseigne_magasin_ids;
    }
}
