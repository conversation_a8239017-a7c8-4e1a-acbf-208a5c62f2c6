<?php

class widget_type_ca_repartition_pie extends widget_type_abstract {

    const AVAILABLE_VARIABLES_FIELDS = array('period');

    public function getDescription() {
        return __(182426,"Affiche sur un diagramme en camembert le chiffre d’affaires HT de la période choisie par catégories de clients ou par activité");
    }

    public function getDescriptionTechnique() {
        return __(182427,'Est basé sur les ventes : Somme des factures en statut "à régler" + "acquittées"');
    }

    public function getPreviewDatas() {
        $preview = new stdClass();
        $preview->sub_title = __(410161,"Sur l'année en cours");
        $preview->montants = [
            ['montant' => 21612, 'lib' => "Client standard"],
            ['montant' => 36478, 'lib' => "Client premium"]
        ];
        return $preview;
    }
    
    public function getDatas($params_supp = array()) {
        $bdd = self::getDataBddInstance();
        $offsetServer = \lmbDateInter::getServerTZOffset(true);
        $offsetEntreprise = \lmbDateInter::getEntrepriseTZOffset(true);        
        $categs = $this->getWidget()->getParam('mode_de_repartition') ?? 'categ_client';
        $querySumMontant = "d.ca_ht";
        switch ($categs){
            case "groupe_centre_de_profit":
                $queryCategSelect="mg.lib";
                $queryCategWhere="";//&& m.id_magasin_groupe IS NOT NULL
                $queryCategGoupBy="m.id_magasin_groupe, mg.lib";
                $queryJoin = "
                    LEFT JOIN magasins m ON m.id_magasin=df.id_magasin
                    LEFT JOIN magasins_groupes mg ON mg.id_magasin_groupe=m.id_magasin_groupe ";
                break;
            case "centre_de_profit":
                $queryCategSelect="m.lib";
                $queryCategWhere="";
                $queryCategGoupBy="df.id_magasin, m.lib";
                $queryJoin = " LEFT JOIN magasins m ON m.id_magasin=df.id_magasin ";
                break;
            case "activite":
                $querySumMontant = "IF(dl.is_chiffre_affaires, dl.montant_ht, 0)";
                $queryCategSelect="act.lib_activite";
                $queryCategWhere="";//&& act.id_activite IS NOT NULL
                $queryCategGoupBy="act.id_activite, act.lib_activite";
                $queryJoin = " 
                    JOIN docs_lines dl ON dl.ref_doc=d.ref_doc
                    JOIN articles art ON art.ref_article=dl.ref_article
                    LEFT JOIN activites act ON act.id_activite=art.id_activite ";
                break;
            case "categ_client":
                $queryCategSelect="cc.lib_client_categ";
                $queryCategWhere="";
                $queryCategGoupBy="ac.id_client_categ, cc.lib_client_categ";
                $queryJoin = "
                    LEFT JOIN annu_client ac ON ac.id_contact = d.id_contact
                    LEFT JOIN clients_categories cc ON cc.id_client_categ = ac.id_client_categ ";
                break;
        }

        $period = $params_supp['period'] ?? [];

        $return = array(
            'sub_title' => empty($period) ? __(410161,"Sur l'année en cours") : __(181676,"Sur la période sélectionnée")
        );

        if(empty($period['date_debut']))
            $period['date_debut'] = date('Y-01-01 00:00:00');
        if(empty($period['date_fin']))
            $period['date_fin'] = date('Y-12-31 23:59:59');

        $query = "SELECT SUM(". $querySumMontant .") as montant, ".$queryCategSelect." as lib
						FROM documents d
						JOIN documents_types dt ON d.id_type_doc = dt.id_type_doc
						JOIN doc_fac df ON df.ref_doc = d.ref_doc
                        ". $queryJoin . "
						WHERE d.id_etat_doc IN (18,19)  
						". $queryCategWhere ."
						 && CONVERT_TZ(d.date_creation_doc,'$offsetServer','$offsetEntreprise') >= " . $bdd->quote($period['date_debut']) ."
                         && CONVERT_TZ(d.date_creation_doc,'$offsetServer','$offsetEntreprise') <= " . $bdd->quote($period['date_fin']) ."
                        GROUP BY ".$queryCategGoupBy."
						ORDER BY CONVERT_TZ(d.date_creation_doc,'$offsetServer','$offsetEntreprise') DESC, d.id_type_doc ASC";

        $return['montants'] = $bdd->query($query)->fetchAll(PDO::FETCH_ASSOC);
        return (object) $return;
    }

    public function getVersion() {
        return 1;
    }

}