<?php

class widget_type_marge_ca_va extends widget_type_abstract {

    const AVAILABLE_VARIABLES_FIELDS = array('id_magasin_enseigne', 'id_magasin', 'period');
    const AVAILABLE_PARAMS = array('comparer_periode_precedente' => '0', 'tarif' => 'ht');

    protected $defaut_cache_ttl = 86400;

    public function getDescription() {
        return __(182412,"Affiche la marge sur le chiffre d’affaires et sur le volume d’affaires ainsi que le pourcentage de celle ci sur le chiffre d’affaires");
    }

    public function getDescriptionTechnique()
    {
        return "<ul><li>".__(182413,"Est basé sur le chiffre d’affaires ou le volume d’affaires")."</li>
            <li>".__(182414,'Chiffre d’affaires : Somme des factures en statut "à régler" + "acquittées"')."</li>
            <li>".__(182415,'Volume d’affaires : Somme des factures en statut "à régler" + "acquittées" + Somme des commandes tous statuts non reliées aux factures en statut "à régler" ou "acquittées"')."</li></ul>";
    }

    public static function getThisParamsLibs() {
        return [
            'comparer_periode_precedente' => static::getInfosPeriodeAnterieureAlt()
        ];
    }

    public function getPreviewDatas(){
        return (object) array(
            'marge_ca' => number_format(8923, 2, ',', ' '),
            'marge_ca_percent_ca' => 89,
            'evo_marge_ca' => 1.93,
            'marge_va' => number_format(9560, 2, ',', ' '),
            'marge_va_percent_ca' => 92,
            'evo_marge_va' => 5.66
        );
    }

    public function getDatas($params_supp = array()) {
        $bdd = PDO_etendu::getInstance();
        $offsetServer = \lmbDateInter::getServerTZOffset(true);
        $offsetEntreprise = \lmbDateInter::getEntrepriseTZOffset(true);
        $where = "";
        $show_evolution = $this->getWidget()->getParam("comparer_periode_precedente", false);
        $tarif = $this->getWidget()->getParam("tarif", "ht");

        if(!empty($params_supp['id_magasin_enseigne']))
            $where .= " AND m.id_magasin_enseigne IN(".$bdd->quoteList($params_supp['id_magasin_enseigne']).")";

        if(!empty($params_supp['id_magasin']))
            $where .= " AND m.id_magasin IN(".$bdd->quoteList($params_supp['id_magasin']).")";

        $last_date_debut = "";
        $last_date_fin = "";

        $period = $params_supp['period'] ?? [];
        if(empty($period['date_debut'])){
            $period['date_debut'] = date('Y-01-01 00:00:00');
            $last_date_debut = $bdd->quote(date('Y-01-01 00:00:00', strtotime("-1 year")));
        }
        if(empty($period['date_fin'])){
            $period['date_fin'] = date('Y-12-31 23:59:59');
            $last_date_fin = $bdd->quote(date('Y-12-31 23:59:59', strtotime("-1 year")));
        }

        $date_debut = $bdd->quote($period['date_debut']);
        $date_fin = $bdd->quote($period['date_fin']);

        if(empty($last_date_debut)) {
            $last_date_debut = $this->getDateYearBefore($period['date_debut']);
            $last_date_debut = $bdd->quote($last_date_debut);
        }
        if(empty($last_date_fin)) {
            $last_date_fin = $this->getDateYearBefore($period['date_fin']);
            $last_date_fin = $bdd->quote($last_date_fin);
        }

        $montant = $tarif == "ht" ? "dl.pu_ht" : "dl.pu_ttc";

        $calculs = array(
            "ROUND(SUM(IF(d.id_type_doc = 4 AND CONVERT_TZ(d.date_creation_doc,'$offsetServer','$offsetEntreprise') >= {$date_debut} AND CONVERT_TZ(d.date_creation_doc,'$offsetServer','$offsetEntreprise') <= {$date_fin}, {$montant} * dl.qte, 0)), 2) ca",
            "ROUND(SUM(IF(d.id_type_doc = 4 AND CONVERT_TZ(d.date_creation_doc,'$offsetServer','$offsetEntreprise') >= {$date_debut} AND CONVERT_TZ(d.date_creation_doc,'$offsetServer','$offsetEntreprise') <= {$date_fin}, ({$montant} - dl.pa_ht) * dl.qte, 0)), 2) marge_ca",
            "ROUND(SUM(IF(CONVERT_TZ(d.date_creation_doc,'$offsetServer','$offsetEntreprise') >= {$date_debut} AND CONVERT_TZ(d.date_creation_doc,'$offsetServer','$offsetEntreprise') <= {$date_fin} AND ( d.id_type_doc = 2 OR ( d.id_type_doc = 4 AND cdc_blc.ref_doc_source IS NULL ) ), ({$montant} - dl.pa_ht) * dl.qte, 0)), 2) marge_va"
        );

        if($show_evolution){
            $calculs[] = "ROUND(SUM(IF(d.id_type_doc = 4 AND CONVERT_TZ(d.date_creation_doc,'$offsetServer','$offsetEntreprise') >= {$last_date_debut} AND CONVERT_TZ(d.date_creation_doc,'$offsetServer','$offsetEntreprise') <= {$last_date_fin}, ({$montant} - dl.pa_ht) * dl.qte, 0)), 2) last_marge_ca";
            $calculs[] = "ROUND(SUM(IF(CONVERT_TZ(d.date_creation_doc,'$offsetServer','$offsetEntreprise') >= {$last_date_debut} AND CONVERT_TZ(d.date_creation_doc,'$offsetServer','$offsetEntreprise') <= {$last_date_fin} AND ( d.id_type_doc = 2 OR ( d.id_type_doc = 4 AND cdc_blc.ref_doc_source IS NULL ) ),({$montant} - dl.pa_ht) * dl.qte, 0)), 2) last_marge_va";
        }

        $select_calculs = implode(", ", $calculs);

        $query = "
            SELECT {$select_calculs}
            FROM documents d
            LEFT JOIN doc_cdc cdc ON cdc.ref_doc = d.ref_doc
            LEFT JOIN doc_fac fac ON fac.ref_doc = d.ref_doc
            LEFT JOIN magasins m ON m.id_magasin = fac.id_magasin OR m.id_magasin = cdc.id_magasin
            JOIN docs_lines dl ON dl.ref_doc = d.ref_doc
            LEFT JOIN documents_liaisons blc_fac ON blc_fac.ref_doc_destination = fac.ref_doc
            LEFT JOIN documents_liaisons cdc_blc ON cdc_blc.ref_doc_destination = blc_fac.ref_doc_source
            WHERE dl.id_doc_line_parent IS NULL AND dl.visible = 1 AND dl.is_chiffre_affaires = 1 AND ( (d.id_type_doc = 2 AND d.id_etat_doc <> 7) OR (d.id_type_doc = 4 AND d.id_etat_doc IN(18,19)) )
            {$where}
        ";

        $results = $bdd->query($query)->fetchObject();

        $evo_marge_ca = $evo_marge_va = 0;
        if($show_evolution){

            $value = intval($results->marge_ca);
            $last = intval($results->last_marge_ca);
            if($last == 0) {
                $evo_marge_ca = "N/D";
            } else {
                $evo_marge_ca = ($last != 0 ? (($value - $last) / $last) * 100 : 100);
            }

            $value = intval($results->marge_va);
            $last = intval($results->last_marge_va);
            if($last == 0) {
                $evo_marge_va = "N/D";
            } else {
                $evo_marge_va = ($last != 0 ? (($value - $last) / $last) * 100 : 100);
            }

        }

        $no_ca = false;
        if($results->ca <= 0){
            $no_ca = true;
            $results->ca = 1;
        }

        return (object) array(
            'marge_ca' => statistiques_widget::roundPriceValue($results->marge_ca),
            'marge_ca_percent_ca' => $no_ca ? 100 : round(($results->marge_ca / $results->ca) * 100),
            'evo_marge_ca' => $evo_marge_ca,
            'marge_va' => statistiques_widget::roundPriceValue($results->marge_va),
            'marge_va_percent_ca' => $no_ca && $results->marge_va <= 0 ? 100 : round(($results->marge_va / $results->ca) * 100),
            'evo_marge_va' => $evo_marge_va
        );
    }

    public function getVersion() {
        return 1;
    }

}
