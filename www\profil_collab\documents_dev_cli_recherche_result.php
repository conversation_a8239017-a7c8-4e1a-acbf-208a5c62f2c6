<?php

// *************************************************************************************************************
// ACCUEIL DE L'UTILISATEUR ADMINISTRATEUR
// *************************************************************************************************************


require ("_dir.inc.php");
require ("_profil.inc.php");
require ($DIR . "_session.inc.php");

if ($_REQUEST['recherche'] != 1)
    die(langage::write("action_non_authorisee"));


$nb_fiches = 0;$nb_fiches = 0;

// Moteur de recherche pour les devis en cours
// *************************************************
// Données pour le formulaire && la requete

$form['page_to_show'] = $search['page_to_show'] = 1;
if (isset($_REQUEST['page_to_show'])) {
    $form['page_to_show'] = $_REQUEST['page_to_show'];
    $search['page_to_show'] = $_REQUEST['page_to_show'];
}
$form['fiches_par_page'] = $search['fiches_par_page'] = !empty(lmbconfig::getInstance()->get("AFF_suivi_cdc_nb_resultats")) ? lmbconfig::getInstance()->get("AFF_suivi_cdc_nb_resultats") : $DOCUMENT_RECHERCHE_SHOWED_FICHES;;
if (isset($_REQUEST['nb_par_pages'])) {
    $form['fiches_par_page'] = $_REQUEST['nb_par_pages'];
    $search['fiches_par_page'] = $_REQUEST['nb_par_pages'];
}
$form['orderby'] = $search['orderby'] = "d.date_creation_doc";
if (isset($_REQUEST['orderby_c'])) {
    $form['orderby'] = $_REQUEST['orderby_c'];
    $search['orderby'] = $_REQUEST['orderby_c'];
}
$form['orderorder'] = $search['orderorder'] = "DESC";
if (isset($_REQUEST['orderorder_c'])) {
    $form['orderorder'] = $_REQUEST['orderorder_c'];
    $search['orderorder'] = $_REQUEST['orderorder_c'];
}
$form['id_client'] = $search['id_client'] = "";
if (isset($_REQUEST['id_client'])) {
    $form['id_client'] = $_REQUEST['id_client'];
    $search['id_client'] = $_REQUEST['id_client'];
}
$form['id_client_categ'] = $search['id_client_categ'] = "";
if (isset($_REQUEST['id_client_categ'])) {
    $form['id_client_categ'] = $_REQUEST['id_client_categ'];
    $search['id_client_categ'] = $_REQUEST['id_client_categ'];
}
$form['transporteur'] = $search['transporteur'] = "";
if (isset($_REQUEST['transporteur'])) {
    $form['transporteur'] = $_REQUEST['transporteur'];
    $search['transporteur'] = $_REQUEST['transporteur'];
}
$form['id_commercial'] = $search['id_commercial'] = "";
if (isset($_REQUEST['commercial_nom'])) {
    $form['id_commercial'] = $_REQUEST['commercial_nom'];
    $search['id_commercial'] = $_REQUEST['commercial_nom'];
}
$form['id_name_mag'] = $search['id_name_mag'] = "";
if (isset($_REQUEST['id_name_magasin_c']) && $_REQUEST['id_name_magasin_c'] != 'all') {
    $form['id_name_mag'] = $_REQUEST['id_name_magasin_c'];
    $search['id_name_mag'] = $_REQUEST['id_name_magasin_c'];
}
$form['article'] = $search['article'] = "";
if (isset($_REQUEST['search_art'])) {
    $form['article'] = $_REQUEST['search_art'];
    $search['article'] = $_REQUEST['search_art'];
}
$form['id_name_categ_art'] = $search['id_name_categ_art'] = "";
if (isset($_REQUEST['search_art_categ'])) {
    $form['id_name_categ_art'] = $_REQUEST['search_art_categ'];
    $search['id_name_categ_art'] = $_REQUEST['search_art_categ'];
}
$form['app_tarifs'] = $search['app_tarifs'] = lmbconfig::getInstance()->get('CAT_app_tarifs_client');
if (isset($_REQUEST['app_tarifs_c'])) {
    $form['app_tarifs'] = $_REQUEST['app_tarifs_c'];
    $search['app_tarifs'] = $_REQUEST['app_tarifs_c'];
}
$form['date_d'] = $search['date_d'] = "";
if (!empty($_REQUEST['date_d'])) {
    $form['date_d'] = date_Fr_to_Us($_REQUEST['date_d']);
    $search['date_d'] = date_Fr_to_Us($_REQUEST['date_d']);
}
$form['date_f'] = $search['date_f'] = "";
if (!empty($_REQUEST['date_f'])) {
    $form['date_f'] = date_Fr_to_Us($_REQUEST['date_f']);
    $search['date_f'] = date_Fr_to_Us($_REQUEST['date_f']);
}
$form['int_dispo'] = $search['int_dispo'] = false;
if (!empty($_REQUEST['dispo_stock'])) {
    $form['int_dispo'] = $_REQUEST['dispo_stock'];
    $search['int_dispo'] = $_REQUEST['dispo_stock'];
}
$form['details'] = $search['details'] = '1';
if (!empty($_REQUEST['details'])) {
    $form['details'] = str_replace("rad", "", $_REQUEST['details']);
    $search['details'] = str_replace("rad", "", $_REQUEST['details']);
}
$form['devperim'] = $search['devperim'] = 0;
if (!empty($_REQUEST['devperim'])) {
    $form['devperim'] = 1;
    $search['devperim'] = 1;
}

$search['etat_c'] = $form['etat_c'] = $_REQUEST['etat_c'];





// *************************************************
// Résultat de la recherche
$fiches = array();
if (isset($_REQUEST['recherche'])) {
    // Préparation de la requete
    $query_join = "";
    $query_where = "1 ";
    $query_limit = (($search['page_to_show'] - 1) * $search['fiches_par_page']) . ", " . $search['fiches_par_page'];
}

// bouton radio : Uniquement les devis en attente de réponse
if ($search['etat_c'] == "dev_attente") {
    $query_where .= " && d.id_etat_doc = 3 ";
}
elseif ($search['etat_c'] == "dev_a_realiser") {
    $query_where .= " && d.id_etat_doc = 1 ";
} else {
    $query_where .= " && d.id_etat_doc IN (1,3) ";
}


// checkbox : uniquement les devis périmés      
if ($search['devperim']) {

    $query_where .= " && (TO_DAYS(NOW()) > TO_DAYS(dd.date_echeance)  )";
}

// Date :: début
if (!empty($search['date_d'])) {
    $query_where .= " && (d.date_creation_doc >= {$bdd->quote(lmbDateInter::ConvertServertoEntrepriseBeginOfDay($search['date_d']))} )";
}
// Date :: fin
if (!empty($search['date_f'])) {
    $query_where .= " && (d.date_creation_doc <= {$bdd->quote(lmbDateInter::ConvertServertoEntrepriseEndOfDay($search['date_f']))} )";
}


// mini-moteur : par commercial
if ($search['id_commercial']) {
    if ($search['id_commercial'] == '__sans__') {
        $query_where .= " && d.ref_doc NOT IN (SELECT ref_doc FROM doc_ventes_commerciaux)";
    } else {

        $query_where .= " && d.ref_doc IN ( SELECT ref_doc FROM doc_ventes_commerciaux dvc
                                            WHERE dvc.id_contact = '" . $search['id_commercial'] . "')";
        $query_join .= " LEFT JOIN doc_ventes_commerciaux dvc ON dvc.ref_doc = d.ref_doc";
    }
}

//	// mini-moteur : par client
//	if ($search['id_client']) {
//		$query_where 	.= " && d.id_contact = '".$search['id_client']."'";
//	}
//	
//	// liste déroulante : par magasin
if ($search['id_name_mag']) {
    $query_where .= " && dd.id_magasin = '" . $search['id_name_mag'] . "'";
}

if ($search['id_client_categ']) {
    $query_where .= " && ac.id_client_categ = " . $bdd->quote($search['id_client_categ']) . "";
    $query_join .= " LEFT JOIN annu_client ac ON ac.id_contact = d.id_contact";
}

// liste déroulante : par catégorie d'article

if ($search['id_name_categ_art']) {

    $query_where .= " && d.ref_doc IN ( SELECT ref_doc FROM docs_lines WHERE ref_article 
                        IN ( SELECT ref_article 
                                FROM articles 
                                WHERE id_art_categ ='" . $search['id_name_categ_art'] . "'
                                        )) ";
}

if (!empty($search['article'])) {
    $query_where .= " && d.ref_doc IN ( SELECT ref_doc FROM docs_lines WHERE ref_article = " . $bdd->quote($search['article']) . ")";
}

$query_where .= " && (d.id_type_doc = '1')";

$magsId = array('null');
$magasins = magasin::charger_all_magasins();
foreach ($magasins as $mag) {
    if(user::getInstance()->checkPermissionByRef(\LMBCore\Permissions\Constants\PermissionRef::ACCES_MAGASINS, $mag->id_magasin)){
    $magsId[] = $mag->id_magasin;
    }
}
$query_where .= "  && dd.id_magasin in ('".implode("','", $magsId)."')";


$query = "SELECT d.ref_doc, d.date_creation_doc, d.code_affaire, d.ref_doc_externe, d.id_type_doc, dt.lib_type_doc, d.id_etat_doc, d.id_devise, dd.date_livraison as date_livraison, de.lib_etat_doc, d.id_contact, nom_contact, dc.id_stock,dd.id_magasin,
						( SELECT SUM(dl.montant_ttc)
							FROM docs_lines_etendues dl
							WHERE d.ref_doc = dl.ref_doc ) as montant_ttc,
						( SELECT SUM(dl.montant_ht )
							FROM docs_lines_etendues dl
							WHERE d.ref_doc = dl.ref_doc ) as montant_ht,
							d.date_creation_doc as date_doc
						FROM documents d
							LEFT JOIN documents_types dt ON d.id_type_doc = dt.id_type_doc
							LEFT JOIN documents_etats de ON d.id_etat_doc = de.id_etat_doc
							LEFT JOIN docs_lines dl ON d.ref_doc = dl.ref_doc
							LEFT JOIN doc_cdc dc ON d.ref_doc = dc.ref_doc
							LEFT JOIN doc_dev dd ON d.ref_doc = dd.ref_doc
							" . $query_join . "
						WHERE " . $query_where . "
						GROUP BY d.ref_doc
						ORDER BY " . addslashes($search['orderby']) . " " . addslashes($search['orderorder']);

session_manager::set('query_liste_devis_client', $query);

$request = $bdd->paginate($query, $search['page_to_show'], $search['fiches_par_page']);
$nb_fiches = $request->total_count;
$fiches2 = $request->fetchAllObject();

$queryStock = request::articles_stock("", "ALL");
$result = $bdd->query($queryStock);

$stockArticles = array();
while ($stockArticle = $result->fetchObject()) {
    $stockArticles[$stockArticle->ref_article][$stockArticle->id_stock] = $stockArticle;
}



//while ($fiche = $request->fetchObject()) {
//while ($fiche = $resultat->fetchObject()) {
foreach ($fiches2 as $fiche) {
    $querY = "SELECT dl.id_doc_line,dl.ref_doc_line,dl.id_doc_line_parent,dl.ref_doc_line_parent,visible,a.lot, dl.ref_doc, dl.ref_article, dl.lib_article, dl.desc_article, dl.qte, dl.pu_ht, dl.remise, dlc.qte_livree, dlc.qte_preparation,
                            a.modele, dl.pu_ht,dl.pu_ttc,
                                            ( SELECT SUM(sa.qte)
                                                FROM stocks_articles sa
                                                WHERE sa.ref_article = dl.ref_article && sa.id_stock = " .$bdd->quote($fiche->id_stock). "
                                            ) as qte_stock
                    FROM docs_lines_etendues_full dl
                    LEFT JOIN articles a ON dl.ref_article = a.ref_article
                    LEFT JOIN doc_lines_cdc dlc ON dl.id_doc_line = dlc.id_doc_line
                    WHERE 	ref_doc = " . $bdd->quote($fiche->ref_doc) . " && dl.ref_article NOT LIKE 'TAXE%'
                    GROUP BY id_doc_line 
                    ORDER BY a.lot DESC,dl.lib_article ASC";
    $result = $bdd->query($querY);
    $fiche->stockAlerte = false;
    $fiche->stockValid = true;

    $magasin = \LMBCore\Societe\Magasin::getInstance($fiche->id_magasin);
    $fiche->id_stock = $magasin->getId_stock();
    $fiche->articles = array();
    while ($article = $result->fetchObject()) {
        $article->stockAlerte = false;
        $article->stockValid = true;
        $bypass = false;
        if ($article->lot == 2)
            $bypass = true;
        if ($article->modele != 'materiel' || $article->qte <= 0) {
            $bypass = true;
        }
        if (!$bypass && (!isset($stockArticles[$article->ref_article][$fiche->id_stock]) || $stockArticles[$article->ref_article][$fiche->id_stock]->qte_stock == 0) && ($article->qte/* -$article->qte_livree */) > 0) {
            $fiche->stockValid = false;
            $article->stockValid = false;
            $bypass = true;
        }
        if (!$bypass && ($article->qte/* -$article->qte_livree */) > $stockArticles[$article->ref_article][$fiche->id_stock]->qte_stock) {
            $fiche->stockValid = false;
            $article->stockValid = false;
            $bypass = true;
        }
        if (!$bypass && ($stockArticles[$article->ref_article][$fiche->id_stock]->qte_res - $stockArticles[$article->ref_article][$fiche->id_stock]->qte_liv) > $stockArticles[$article->ref_article][$fiche->id_stock]->qte_stock) {
            if (/* $fiche->id_etat_doc == 3 || /*$fiche->id_etat_doc == 74 && */( $article->qte/* -$article->qte_livree-$article->qte_preparation */) > 0) {
                if (($article->qte/* -$article->qte_livree-$article->qte_preparation */) > $stockArticles[$article->ref_article]->qte_stock - $stockArticles[$article->ref_article]->qte_prep) {
                    $fiche->stockValid = false;
                    $article->stockValid = false;
                } else {
                    $fiche->stockAlerte = true;
                    $article->stockAlerte = true;
                }
                $bypass = true;
            }
        }

        $fiche->articles[$article->id_doc_line] = $article;
        if (!empty($article->id_doc_line_parent) /* && ($article->stockAlerte || !$article->stockValid) */) {
            if (!empty($fiche->articles[$article->id_doc_line_parent])) {
                if ($article->stockAlerte) {
                    $fiche->articles[$article->id_doc_line_parent]->stockAlerte = true;
                    $fiche->stockAlerte = true;
                }
                if (!$article->stockValid) {
                    $fiche->articles[$article->id_doc_line_parent]->stockValid = false;
                    $fiche->stockValid = false;
                }
            }
        }
    }

    if ($search['int_dispo']) {
        if ($search['int_dispo'] == "dispo" && $fiche->stockValid == true)
            $fiches[] = $fiche;
        elseif ($search['int_dispo'] == "non_dispo" && $fiche->stockValid != true)
            $fiches[] = $fiche;
    } else
        $fiches[] = $fiche;
}


// *************************************************************************************************************
// AFFICHAGE
// *************************************************************************************************************

include ($THIS_DIR . "pages/page_documents_dev_cli_recherche_result_new.inc.php");
?>