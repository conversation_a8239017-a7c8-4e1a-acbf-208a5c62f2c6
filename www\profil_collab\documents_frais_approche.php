<?php
require ("_dir.inc.php");
require ($DIR."_session.inc.php");
global $REF_ARTICLE_ESCOMPTE;
global $LIVRAISON_MODE_ART_CATEG;

// *************************************************************************************************************
// VARIABLES
// *************************************************************************************************************

$ref_doc  = $_REQUEST['ref_doc'];
$document = open_doc($_REQUEST['ref_doc']);

$liste_contenu = $document->getContenu(true);
$all_devises = devise::getDevise_active_list();
$default_devise = devise::getDefaut();

$TheCW = $document->getQuantityAndWeigth();
$quantiteTotal = 0;
$montant_ht = 0;
foreach ($liste_contenu as $contenu){
    if ($contenu->modele != "materiel" || $contenu->visible == 0){
        continue;
    }
    $quantiteTotal += $contenu->qte;
    $montant_ht += ($contenu->pu_ht - $contenu->remise)*$contenu->qte;
}
$poidsTotal = $TheCW[1];

// *************************************************************************************************************
// GESTION DES ACTIONS
// *************************************************************************************************************

if(isset($_REQUEST['action']) && $_REQUEST['action'] == "add" && isset($_REQUEST['id_frais_type']) && isset($_REQUEST['clé'])){
    foreach ($liste_contenu as $contenu){
        if ($contenu->modele != "materiel" || $contenu->visible == 0){
            continue;
        }
        $detailPu = doc_line_details_pu::getInstance(document::getIdFromRef_doc_line($contenu->ref_doc_line));            
        $frais_line = $detailPu->isFrais() ? $detailPu->getInstances() : array();
        if(!is_array($frais_line)){
            $frais_line = array_values(get_object_vars($frais_line));
        }
        $frais_obj = new stdClass();
        $frais_obj->id_frais_type = $_REQUEST['id_frais_type'];
        $frais_obj->id_devise = !empty($_REQUEST['id_devise'])?$_REQUEST['id_devise']:$default_devise->getId_devise();
        $frais_obj->taux_change = !empty($_REQUEST['taux_change'])?$_REQUEST['taux_change']:devise::getInstance($frais_obj->id_devise)->getTaux_de_change();
        if(empty($_REQUEST['clé'])){
            $type_frais = LMBCore\Frais\TypeFrais::getInstance($_REQUEST['id_frais_type']);
            $_REQUEST['clé'] = \LMBCore\Frais\FraisApproche::getKeyFromLib($type_frais->getDefaut_cle_repartition());
        }
        $frais_obj->formule_calcul = \LMBCore\Frais\FraisApproche::getFormuleFromValueAndKey($_REQUEST['valeur'], $_REQUEST['clé']);
        if(!empty($frais_line)){
            $test = true;
            foreach($frais_line as &$frais){
                $frais = (object)$frais;
                if($frais->id_frais_type == $frais_obj->id_frais_type){
                    $frais = $frais_obj;
                    $test = false;
                }
            }
            if($test){
                $frais_line[] = $frais_obj;
            }
        } else {
            $frais_line[] = $frais_obj;
        }
        asort($frais_line);
        $detailPu->setInstances($frais_line)->save();
        unset($frais_line);
    }
    if($_REQUEST['clé'] == \LMBCore\Frais\FraisApproche::CLE_POIDS && $poidsTotal == 0){
        echo "<script>
                LMBTools.alert({
                    title: '".__(122042,"Répartition des frais d\'approche")."',
                    content: '".__(122043,"Le poids total des articles est nul.")."'
                });</script>";
    }
    $liste_contenu = $document->getContenu(true);
}
if(isset($_REQUEST['action']) && $_REQUEST['action'] == "sup" && isset($_REQUEST['id_frais_type'])){
    foreach ($liste_contenu as $contenu){
        if ($contenu->modele != "materiel" || $contenu->visible == 0){
            continue;
        }
        $detailPu = doc_line_details_pu::getInstance(document::getIdFromRef_doc_line($contenu->ref_doc_line));            
        $frais_line = $detailPu->isFrais() ? $detailPu->getInstances() : array();
        if(!empty($frais_line)){
            $new_frais_line = array();
            foreach($frais_line as &$frais){
                $frais = (object)$frais;
                if($frais->id_frais_type != $_REQUEST['id_frais_type']){
                    $new_frais_line[] = $frais;
                }
            }
            if(!is_array($new_frais_line)){
                $new_frais_line = get_object_vars($new_frais_line);
            }
            asort($new_frais_line);
            $detailPu->setInstances($new_frais_line)->save();
            unset($frais_line);
        }
    }
    $liste_contenu = $document->getContenu(true);
}
if(isset($_REQUEST['action']) && ($_REQUEST['action'] == "maj_art_ha") && isset($_REQUEST['value']) && isset($_REQUEST['ref_article'])){
    foreach ($liste_contenu as $contenu){
        if ($contenu->modele != "materiel" || $contenu->visible == 0){
            continue;
        }
        if($contenu->ref_article == $_REQUEST['ref_article'] && $_REQUEST['value'] > 0){
            $frais_obj = new stdClass();
            $frais_obj->id_frais_type = (\LMBCore\Frais\TypeFrais::getFirstTypeFrais(\LMBCore\Frais\TypeFrais::frais_approche))?\LMBCore\Frais\TypeFrais::getFirstTypeFrais(\LMBCore\Frais\TypeFrais::frais_approche)->getId_frais_type():0;
            $frais_obj->id_devise = $default_devise->getId_devise();
            $frais_obj->taux_change = $default_devise->getTaux_de_change();
            $frais_obj->formule_calcul = $_REQUEST['value'];
            $new_frais_line = array($frais_obj);
            if(!is_array($new_frais_line)){
                $new_frais_line = get_object_vars($new_frais_line);
            }
            asort($new_frais_line);
            doc_line_details_pu::getInstance(document::getIdFromRef_doc_line($contenu->ref_doc_line))->setInstances($new_frais_line)->save();
        } else if($contenu->ref_article == $_REQUEST['ref_article'] && $_REQUEST['value'] == 0){
            doc_line_details_pu::getInstance(document::getIdFromRef_doc_line($contenu->ref_doc_line))->setInstances(array())->save();
        } else {
            $detailPu = doc_line_details_pu::getInstance(document::getIdFromRef_doc_line($contenu->ref_doc_line));
            $frais_line = $detailPu->isFrais() ? $detailPu->getInstances() : array();
            foreach($frais_line as &$frais){
                $frais = (object)$frais;
                $article = \article::getInstance($contenu->ref_article);
                if($article->isStockable()){
                    $colis = $article->getColis();
                }
                $frais_approche = new \LMBCore\Frais\FraisApproche(null,$frais->formule_calcul);
                $frais->formule_calcul = $frais_approche->getMontant($contenu->pu_ht, $contenu->remise, $montant_ht, $quantiteTotal, floatval(\LMBCore\Article\UniteGestion::convertValue(empty($colis[0]->id_carac_poids)?8:$colis[0]->id_carac_poids, empty($colis[0]->poids)?0:$colis[0]->poids, true)), $poidsTotal);
            }
            if(!is_array($frais_line)){
                $frais_line = get_object_vars($frais_line);
            }
            asort($frais_line);
            $detailPu->setInstances($frais_line)->save();
            unset($frais_line);
        }
    }
    $liste_contenu = $document->getContenu(true);
}
if(isset($_REQUEST['action']) && ($_REQUEST['action'] == "sup_art" || $_REQUEST['action'] == "maj_art") && isset($_REQUEST['id_frais_type']) && isset($_REQUEST['ref_article'])){
    foreach ($liste_contenu as $contenu){
        if ($contenu->modele != "materiel" || $contenu->visible == 0){
            continue;
        }
        $detailPu = doc_line_details_pu::getInstance(document::getIdFromRef_doc_line($contenu->ref_doc_line));            
        $frais_line = $detailPu->isFrais() ? $detailPu->getInstances() : array();
        if(!empty($frais_line)){
            if($contenu->ref_article == $_REQUEST['ref_article']){
                $new_frais_line = array();
                foreach($frais_line as &$frais){
                    $frais = (object)$frais;
                    if($frais->id_frais_type != $_REQUEST['id_frais_type']){
                        $new_frais_line[] = $frais;
                    } else if($_REQUEST['action'] == "maj_art"){
                        $frais_obj = new stdClass();
                        $frais_obj->id_frais_type = $_REQUEST['id_frais_type'];
                        $frais_obj->id_devise = !empty($_REQUEST['id_devise'])?$_REQUEST['id_devise']:$default_devise->getId_devise();
                        $frais_obj->taux_change = (empty($_REQUEST['id_devise']) || $_REQUEST['id_devise']==$default_devise->getId_devise())?"":(!empty($_REQUEST['taux_change'])?$_REQUEST['taux_change']:devise::getInstance($frais_obj->id_devise)->getTaux_de_change());
                        $frais_obj->formule_calcul = !empty($_REQUEST['valeur'])?$_REQUEST['valeur']:"0";
                        $new_frais_line[] = $frais_obj;
                    }
                }
                if(!is_array($new_frais_line)){
                    $new_frais_line = get_object_vars($new_frais_line);
                }
                asort($new_frais_line);
                $detailPu->setInstances($new_frais_line)->save();
            } else {
                foreach($frais_line as &$frais){
                    $frais = (object) $frais;
                    if($frais->id_frais_type == $_REQUEST['id_frais_type']){
                        $article = \article::getInstance($contenu->ref_article);
                        if($article->isStockable()){
                            $colis = $article->getColis();
                        }
                        $frais_approche = new \LMBCore\Frais\FraisApproche(null,$frais->formule_calcul);
                        $frais->formule_calcul = $frais_approche->getMontant($contenu->pu_ht, $contenu->remise, $montant_ht, $quantiteTotal, floatval(\LMBCore\Article\UniteGestion::convertValue(empty($colis[0]->id_carac_poids)?8:$colis[0]->id_carac_poids, empty($colis[0]->poids)?0:$colis[0]->poids, true)), $poidsTotal);
                    }
                }
                if(!is_array($frais_line)){
                    $frais_line = get_object_vars($frais_line);
                }
                asort($frais_line);
                $detailPu->setInstances($frais_line)->save();   
            }
        }
        unset($frais_line);
    }
    $liste_contenu = $document->getContenu(true);
}
if(isset($_REQUEST['action']) && $_REQUEST['action'] == "maj" && isset($_REQUEST['id_frais_type']) && isset($_REQUEST['clé']) && $_REQUEST['clé'] != \LMBCore\Frais\FraisApproche::CLE_MANUELLE){
    $frais_obj = new stdClass();
    $frais_obj->id_frais_type = $_REQUEST['id_frais_type'];
    $frais_obj->id_devise = !empty($_REQUEST['id_devise'])?$_REQUEST['id_devise']:$default_devise->getId_devise();
    $frais_obj->taux_change = !empty($_REQUEST['taux_change'])?$_REQUEST['taux_change']:devise::getInstance($frais_obj->id_devise)->getTaux_de_change();
    $frais_obj->formule_calcul = \LMBCore\Frais\FraisApproche::getFormuleFromValueAndKey($_REQUEST['valeur'], $_REQUEST['clé']);
    foreach ($liste_contenu as $contenu){
        if ($contenu->modele != "materiel" || $contenu->visible == 0){
            continue;
        }
        $detailPu = doc_line_details_pu::getInstance(document::getIdFromRef_doc_line($contenu->ref_doc_line));            
        $frais_line = $detailPu->isFrais() ? $detailPu->getInstances() : array();
        if(!empty($frais_line)){
            $frais_non_existant = true;
            foreach($frais_line as &$frais){
                $frais = (object)$frais;
                if($frais->id_frais_type == $_REQUEST['id_frais_type']){
                    $frais_non_existant = false;
                    $frais = $frais_obj;
                    break;
                }
            }
            if($frais_non_existant){
                $frais_line[] = $frais_obj;
            }
        } else {
            $frais_line[] = $frais_obj;
        }
        if(!is_array($frais_line)){
            $frais_line = get_object_vars($frais_line);
        }
        asort($frais_line);
        $detailPu->setInstances($frais_line)->save();
        unset($frais_line);
    }
    if($_REQUEST['clé'] == \LMBCore\Frais\FraisApproche::CLE_POIDS && $poidsTotal == 0){
        echo "<script>
                LMBTools.alert({
                    title: '".__(122003,"Répartition des frais d\'approche")."',
                    content: '".__(122004,"Le poids total des articles est nul.")."'
                });</script>";
    }
    $liste_contenu = $document->getContenu(true);
}

if(isset($_REQUEST['action']) && $_REQUEST['action'] == "maj" && isset($_REQUEST['id_frais_type']) && isset($_REQUEST['clé']) && $_REQUEST['clé'] == \LMBCore\Frais\FraisApproche::CLE_MANUELLE) {
    // maj devise ou taux de change seulement
    if(!empty($_REQUEST['id_devise']) || !empty($_REQUEST['taux_change'])) {
        $new_id_devise = !empty($_REQUEST['id_devise']) ? $_REQUEST['id_devise'] : $default_devise->getId_devise();
        $new_taux_change = !empty($_REQUEST['taux_change']) ? $_REQUEST['taux_change'] : devise::getInstance($frais_obj->id_devise)->getTaux_de_change();
        foreach ($liste_contenu as $contenu) {
            if ($contenu->modele != "materiel" || $contenu->visible == 0) {
                continue;
            }
            $detailPu = doc_line_details_pu::getInstance(document::getIdFromRef_doc_line($contenu->ref_doc_line));            
            $frais_line = $detailPu->isFrais() ? $detailPu->getInstances() : array();
            if (!empty($frais_line)) {
                foreach ($frais_line as &$frais) {
                    $frais = (object)$frais;
                    if ($frais->id_frais_type == $_REQUEST['id_frais_type']) {
                        $frais->id_devise = $new_id_devise;
                        $frais->taux_change = $new_taux_change;
                        break;
                    }
                }
            }
            if (!is_array($frais_line)) {
                $frais_line = get_object_vars($frais_line);
            }
            asort($frais_line);
            $detailPu->setInstances($frais_line)->save();
            unset($frais_line);
        }
    }
    $liste_contenu = $document->getContenu(true);
}

if(isset($_REQUEST['action']) && $_REQUEST['action'] == "propagate"){
    $listes = array_filter($document->getContenu(), function($contenu){
        if ($contenu->modele != "materiel" || $contenu->visible == 0) {
            return false;
        }
        return true;
    });
    foreach ($listes as $line) {
        $detailPu = doc_line_details_pu::getInstance(document::getIdFromRef_doc_line($line->ref_doc_line));
        $frais_line = $detailPu->isFrais() ? $detailPu->getInstances() : array();
        $article = article::getInstance($line->ref_article);
        \article_frais::del_for_article_and_fournisseur($article->getId_article(), $document->getId_contact());
        $frais_port = new article_frais($line->ref_article);
        if(!empty($frais_line)){
            foreach($frais_line as $frais){
                $frais = (object)$frais;
                $article = \article::getInstance($line->ref_article);
                if($article->isStockable()){
                    $colis = $article->getColis();
                }
                $frais_approche = new \LMBCore\Frais\FraisApproche($frais->id_frais_type,$frais->formule_calcul, null, null, $frais->id_devise, $frais->taux_change);
                $montant = $frais_approche->getMontant($line->pu_ht, $line->remise, $montant_ht, $quantiteTotal, floatval(\LMBCore\Article\UniteGestion::convertValue(empty($colis[0]->id_carac_poids)?8:$colis[0]->id_carac_poids, empty($colis[0]->poids)?0:$colis[0]->poids, true)), $poidsTotal);
                $frais_toto = empty($frais->taux_change) ? $montant : $montant/$frais->taux_change;
                $document->maj_line_pu_ttc($line->id_doc_line, ($line->pu_ttc + $frais_toto), true);
                $frais_port->add($frais->id_frais_type, $montant, null, $document->getId_contact(), $frais->id_devise, $frais->taux_change, false, $document->getRef_doc());
            }
        }
        if ($line->type_of_line != 'article') continue;
        if ($line->id_modele_spe) continue;
        if (!$line->visible) continue;

        $propagateFraisUpdateDocLines = true;

        // on charge l'article
        if (!$article->add_ref_article_externe($document->getId_contact(), $line->ref_article_externe, $line->lib_article, $line->pu_ht*$document->getTaux_conversion(), $document->getDate_creation(), $document->getId_devise(), $line->remise, $line->qte, $document->getTaux_conversion())) {
            $GLOBALS['_ALERTES'] = array();
            LMBNotices::reset();
            $article->mod_ref_article_externe($document->getId_contact(), $document->getId_contact(), $line->ref_article_externe, $line->ref_article_externe, $line->lib_article, $line->pu_ht*$document->getTaux_conversion(), $document->getDate_creation(), $document->getId_devise(), $line->remise, $line->qte, $document->getTaux_conversion());
        }
    }
    $liste_contenu = $document->getContenu(true);
    foreach ($liste_contenu as $contenu){
        if($contenu->modele != "materiel" || $contenu->visible == 0){
            continue;
        }
        $detailPu = doc_line_details_pu::getInstance(document::getIdFromRef_doc_line($contenu->ref_doc_line));            
        $frais_line = $detailPu->isFrais() ? $detailPu->getInstances() : array();
        $detailPu->setInstances(array())->save();
        unset($frais_line);
    }
    echo "<script>
                LMBTools.alert({
                    title: '".__(121385,"Propager sur les articles")."',
                    content: '".__(121386,"Les frais d\'approche ont été modifiés avec succès")."'
                });</script>";
    $liste_contenu = $document->getContenu(true);
}

// *************************************************************************************************************
// CHARGEMENT DES FRAIS
// *************************************************************************************************************

$document->frais_used = array();
$listess = array_filter($liste_contenu, function($contenu){
    $contenu->frais_toto = 0;
    if ($contenu->modele != "materiel" || $contenu->visible == 0) {
        return false;
    }
    return true;
});
foreach ($listess as $index_line => $contenu){
    $detailPu = doc_line_details_pu::getInstance(document::getIdFromRef_doc_line($contenu->ref_doc_line));          
    $frais_line = $detailPu->isFrais() ? $detailPu->getInstances() : array();
    $frais_port = new article_frais($contenu->ref_article);
    $frais_port->getFrais();
    $contenu->frais_toto = 0;
    $contenu->pt_ht = $contenu->pu_ht;
    if(!empty($frais_line)){
        foreach($frais_line as $frais){
            $frais = (object)$frais;
            $article = \article::getInstance($contenu->ref_article);
            if($article->isStockable()){
                $colis = $article->getColis();
            }
            $frais_approche = new \LMBCore\Frais\FraisApproche(null,$frais->formule_calcul);
            $montant = $frais_approche->getMontant($contenu->pu_ht, $contenu->remise, $document->getMontant_ht(), $quantiteTotal, floatval(\LMBCore\Article\UniteGestion::convertValue(empty($colis[0]->id_carac_poids)?8:$colis[0]->id_carac_poids, empty($colis[0]->poids)?0:$colis[0]->poids, true)), $poidsTotal);
            $cle = $frais_approche->getCle_repartition();
            $contenu->frais_toto += empty($frais->taux_change) ? $montant : $montant/$frais->taux_change;
            if(isset($document->frais_used[$frais->id_frais_type])){
                $document->frais_used[$frais->id_frais_type]["clé"] = $cle;
                $document->frais_used[$frais->id_frais_type]["valeur"] += $montant*$contenu->qte;
                $document->frais_used[$frais->id_frais_type]["total"] = $frais_approche->getValeur();
            } else {
                $document->frais_used[$frais->id_frais_type] = array("valeur"=>$montant*$contenu->qte,"total"=>$frais_approche->getValeur(),"id_devise"=>$frais->id_devise,"taux_change"=>$frais->taux_change,"clé" => ($index_line == 0)?$cle:"4");
            }
        }
    } else {
        $frais_contenu = array();
        foreach ($frais_port->getFrais() as $frais){
            
            $typeFrais = new LMBCore\Frais\TypeFrais($frais->id_frais_type);
            
            if($document->getId_contact() && $document->getId_contact() == $frais->id_fournisseur && $typeFrais->getFrais_famille() == LMBCore\Frais\TypeFrais::frais_approche){
                if($frais->type_calcul==\LMBCore\Frais\FraisApproche::SUFFIXE_POURCENT){
                    $frais->montant = floatval($frais->val_calcul) * ($contenu->pu_ht - $contenu->remise * $contenu->pu_ht / 100) / 100;
                } else {
                    $frais->montant = $frais->val_calcul;
                }                $frais_obj = new stdClass();
                $frais_obj->id_frais_type = $frais->id_frais_type;
                $frais_obj->id_devise = empty($frais->id_devise)?$document->getId_devise():$frais->id_devise;
                $frais_obj->taux_change = empty($frais->taux_change)?devise::getInstance($frais_obj->id_devise)->getTaux_de_change():$frais->taux_change;
                $frais_obj->formule_calcul = $frais->montant;
                $frais_contenu[] = $frais_obj;
                if(isset($document->frais_used[$frais->id_frais_type])){
                    $document->frais[$frais->id_frais_type]["clé"] = \LMBCore\Frais\FraisApproche::CLE_MANUELLE;
                }
            }
        }
        if(!is_array($frais_contenu)){
            $frais_contenu = get_object_vars($frais_contenu);
        }
        asort($frais_contenu);
    }
}


// *************************************************************************************************************
// AFFICHAGE
// *************************************************************************************************************

try{
    include ($THIS_DIR."pages/page_documents_frais_approche.inc.php");
} catch(Throwable $e){
    ob_clean();
    ?>
        <div class="portlet bg-error">
            <div class="portlet-body text-center">
                <?php echo $e->getMessage() ?>
            </div>
        </div>
        <script type="text/javascript">
            H_loading();
        </script>
    <?php
}
?>
